2025-06-07 16:02:48.630 [info] [main] Log level: Info
2025-06-07 16:02:48.630 [info] [main] Validating found git in: "git"
2025-06-07 16:02:48.630 [info] [main] Using git "2.47.2" from "git"
2025-06-07 16:02:48.630 [info] [Model][doInitialScan] Initial repository scan started
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [11ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --git-dir --git-common-dir [4ms]
2025-06-07 16:02:48.630 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-07 16:02:48.630 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 16:02:48.630 [info] > git config --get commit.template [7ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [5ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 16:02:48.630 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [4ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [23ms]
2025-06-07 16:02:48.630 [info] > git status -z -uall [31ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [4ms]
2025-06-07 16:02:48.630 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [23ms]
2025-06-07 16:02:48.630 [info] > git config --get commit.template [24ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [28ms]
2025-06-07 16:02:48.630 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [118ms]
2025-06-07 16:02:48.630 [info] > git config --get --local branch.main.vscode-merge-base [16ms]
2025-06-07 16:02:48.630 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [20ms]
2025-06-07 16:02:48.631 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [61ms]
2025-06-07 16:02:48.703 [info] > git rev-parse --show-toplevel [116ms]
2025-06-07 16:02:48.730 [info] > git status -z -uall [24ms]
2025-06-07 16:02:48.730 [info] > git merge-base refs/heads/main refs/remotes/origin/main [94ms]
2025-06-07 16:02:48.745 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [36ms]
2025-06-07 16:02:48.751 [info] > git rev-parse --show-toplevel [8ms]
2025-06-07 16:02:48.751 [info] > git diff --name-status -z --diff-filter=ADMR afcc68bbfc7a9840fe8bc9258ce0aab95444c453...refs/remotes/origin/main [15ms]
2025-06-07 16:02:48.755 [info] > git merge-base refs/heads/main refs/remotes/origin/main [6ms]
2025-06-07 16:02:48.768 [info] > git rev-parse --show-toplevel [13ms]
2025-06-07 16:02:48.768 [info] > git diff --name-status -z --diff-filter=ADMR afcc68bbfc7a9840fe8bc9258ce0aab95444c453...refs/remotes/origin/main [10ms]
2025-06-07 16:02:48.774 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 16:02:48.777 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-07 16:02:53.730 [info] > git config --get commit.template [2ms]
2025-06-07 16:02:53.736 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:02:53.747 [info] > git status -z -uall [7ms]
2025-06-07 16:02:53.750 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:03:01.733 [info] > git config --get commit.template [4ms]
2025-06-07 16:03:01.735 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:03:01.743 [info] > git status -z -uall [5ms]
2025-06-07 16:03:01.744 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:03:06.756 [info] > git config --get commit.template [2ms]
2025-06-07 16:03:06.766 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:03:06.777 [info] > git status -z -uall [7ms]
2025-06-07 16:03:06.780 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:03:11.791 [info] > git config --get commit.template [2ms]
2025-06-07 16:03:11.798 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:03:11.811 [info] > git status -z -uall [4ms]
2025-06-07 16:03:11.814 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:03:16.823 [info] > git config --get commit.template [0ms]
2025-06-07 16:03:16.828 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:03:16.837 [info] > git status -z -uall [6ms]
2025-06-07 16:03:16.838 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:03:21.855 [info] > git config --get commit.template [8ms]
2025-06-07 16:03:21.857 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:03:21.881 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:03:21.884 [info] > git status -z -uall [16ms]
2025-06-07 16:03:26.902 [info] > git config --get commit.template [1ms]
2025-06-07 16:03:26.918 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 16:03:26.943 [info] > git status -z -uall [11ms]
2025-06-07 16:03:26.946 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:03:31.961 [info] > git config --get commit.template [2ms]
2025-06-07 16:03:31.966 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:03:31.976 [info] > git status -z -uall [6ms]
2025-06-07 16:03:31.976 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:03:36.995 [info] > git config --get commit.template [6ms]
2025-06-07 16:03:36.998 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:03:37.014 [info] > git status -z -uall [9ms]
2025-06-07 16:03:37.016 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:03:42.064 [info] > git config --get commit.template [13ms]
2025-06-07 16:03:42.076 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:03:42.106 [info] > git status -z -uall [11ms]
2025-06-07 16:03:42.107 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:03:47.127 [info] > git config --get commit.template [1ms]
2025-06-07 16:03:47.137 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 16:03:47.149 [info] > git status -z -uall [6ms]
2025-06-07 16:03:47.150 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:03:52.163 [info] > git config --get commit.template [3ms]
2025-06-07 16:03:52.172 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:03:52.197 [info] > git status -z -uall [9ms]
2025-06-07 16:03:52.197 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:03:57.216 [info] > git config --get commit.template [1ms]
2025-06-07 16:03:57.229 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:03:57.274 [info] > git status -z -uall [23ms]
2025-06-07 16:03:57.275 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:04:02.290 [info] > git config --get commit.template [7ms]
2025-06-07 16:04:02.292 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:04:02.316 [info] > git status -z -uall [9ms]
2025-06-07 16:04:02.318 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:04:07.335 [info] > git config --get commit.template [7ms]
2025-06-07 16:04:07.336 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:04:07.357 [info] > git status -z -uall [10ms]
2025-06-07 16:04:07.361 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:04:12.375 [info] > git config --get commit.template [5ms]
2025-06-07 16:04:12.384 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:04:12.419 [info] > git status -z -uall [20ms]
2025-06-07 16:04:12.419 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:04:19.856 [info] > git config --get commit.template [11ms]
2025-06-07 16:04:19.859 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:04:19.881 [info] > git status -z -uall [13ms]
2025-06-07 16:04:19.884 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:04:24.893 [info] > git config --get commit.template [3ms]
2025-06-07 16:04:24.894 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:04:24.901 [info] > git status -z -uall [4ms]
2025-06-07 16:04:24.902 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:04:29.917 [info] > git config --get commit.template [5ms]
2025-06-07 16:04:29.918 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:04:29.931 [info] > git status -z -uall [7ms]
2025-06-07 16:04:29.932 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:04:34.946 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:04:34.946 [info] > git config --get commit.template [5ms]
2025-06-07 16:04:34.953 [info] > git status -z -uall [4ms]
2025-06-07 16:04:34.954 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:04:39.966 [info] > git config --get commit.template [4ms]
2025-06-07 16:04:39.968 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:04:39.979 [info] > git status -z -uall [5ms]
2025-06-07 16:04:39.980 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:04:46.044 [info] > git config --get commit.template [4ms]
2025-06-07 16:04:46.053 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:04:46.085 [info] > git status -z -uall [18ms]
2025-06-07 16:04:46.086 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:04:51.099 [info] > git config --get commit.template [2ms]
2025-06-07 16:04:51.104 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:04:51.113 [info] > git status -z -uall [6ms]
2025-06-07 16:04:51.116 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:05:45.210 [info] > git config --get commit.template [3ms]
2025-06-07 16:05:45.212 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:05:45.218 [info] > git status -z -uall [3ms]
2025-06-07 16:05:45.219 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:05:50.309 [info] > git config --get commit.template [3ms]
2025-06-07 16:05:50.311 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:05:50.322 [info] > git status -z -uall [4ms]
2025-06-07 16:05:50.323 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:05:55.335 [info] > git config --get commit.template [4ms]
2025-06-07 16:05:55.337 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:05:55.343 [info] > git status -z -uall [3ms]
2025-06-07 16:05:55.344 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:06:00.357 [info] > git config --get commit.template [6ms]
2025-06-07 16:06:00.358 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:06:00.365 [info] > git status -z -uall [4ms]
2025-06-07 16:06:00.367 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:06:05.378 [info] > git config --get commit.template [1ms]
2025-06-07 16:06:05.385 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:06:05.396 [info] > git status -z -uall [6ms]
2025-06-07 16:06:05.397 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:06:10.410 [info] > git config --get commit.template [5ms]
2025-06-07 16:06:10.412 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:06:10.563 [info] > git status -z -uall [146ms]
2025-06-07 16:06:10.605 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [182ms]
2025-06-07 16:06:15.622 [info] > git config --get commit.template [3ms]
2025-06-07 16:06:15.623 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:06:15.634 [info] > git status -z -uall [5ms]
2025-06-07 16:06:15.635 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:06:20.647 [info] > git config --get commit.template [5ms]
2025-06-07 16:06:20.648 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:06:20.656 [info] > git status -z -uall [4ms]
2025-06-07 16:06:20.657 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:06:25.668 [info] > git config --get commit.template [4ms]
2025-06-07 16:06:25.669 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:06:25.676 [info] > git status -z -uall [3ms]
2025-06-07 16:06:25.678 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:06:30.690 [info] > git config --get commit.template [4ms]
2025-06-07 16:06:30.691 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:06:30.698 [info] > git status -z -uall [3ms]
2025-06-07 16:06:30.699 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:06:35.712 [info] > git config --get commit.template [4ms]
2025-06-07 16:06:35.713 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:06:35.721 [info] > git status -z -uall [4ms]
2025-06-07 16:06:35.722 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:06:40.734 [info] > git config --get commit.template [5ms]
2025-06-07 16:06:40.735 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:06:40.742 [info] > git status -z -uall [4ms]
2025-06-07 16:06:40.743 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:06:45.756 [info] > git config --get commit.template [4ms]
2025-06-07 16:06:45.757 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:06:45.767 [info] > git status -z -uall [7ms]
2025-06-07 16:06:45.767 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:06:50.784 [info] > git config --get commit.template [7ms]
2025-06-07 16:06:50.787 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:06:50.802 [info] > git status -z -uall [9ms]
2025-06-07 16:06:50.804 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:06:57.516 [info] > git config --get commit.template [14ms]
2025-06-07 16:06:57.517 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:06:57.528 [info] > git status -z -uall [4ms]
2025-06-07 16:06:57.530 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:07:02.544 [info] > git config --get commit.template [5ms]
2025-06-07 16:07:02.545 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:07:02.553 [info] > git status -z -uall [4ms]
2025-06-07 16:07:02.554 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:18:25.721 [info] > git config --get commit.template [2ms]
2025-06-07 16:18:25.737 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:18:25.850 [info] > git status -z -uall [97ms]
2025-06-07 16:18:25.851 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [80ms]
2025-06-07 16:18:30.885 [info] > git config --get commit.template [3ms]
2025-06-07 16:18:30.918 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:18:30.981 [info] > git status -z -uall [51ms]
2025-06-07 16:18:30.982 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [36ms]
2025-06-07 16:18:36.037 [info] > git config --get commit.template [24ms]
2025-06-07 16:18:36.039 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:18:36.078 [info] > git status -z -uall [18ms]
2025-06-07 16:18:36.099 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [20ms]
2025-06-07 16:18:41.141 [info] > git config --get commit.template [25ms]
2025-06-07 16:18:41.143 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:18:41.175 [info] > git status -z -uall [15ms]
2025-06-07 16:18:41.178 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:18:46.204 [info] > git config --get commit.template [9ms]
2025-06-07 16:18:46.206 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:18:46.230 [info] > git status -z -uall [13ms]
2025-06-07 16:18:46.232 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:18:51.276 [info] > git config --get commit.template [9ms]
2025-06-07 16:18:51.277 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:18:51.289 [info] > git status -z -uall [6ms]
2025-06-07 16:18:51.291 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:18:56.309 [info] > git config --get commit.template [6ms]
2025-06-07 16:18:56.310 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:18:56.323 [info] > git status -z -uall [5ms]
2025-06-07 16:18:56.324 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:19:01.350 [info] > git config --get commit.template [10ms]
2025-06-07 16:19:01.351 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:19:01.365 [info] > git status -z -uall [5ms]
2025-06-07 16:19:01.366 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:19:06.383 [info] > git config --get commit.template [5ms]
2025-06-07 16:19:06.385 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:19:06.398 [info] > git status -z -uall [6ms]
2025-06-07 16:19:06.399 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:19:11.416 [info] > git config --get commit.template [6ms]
2025-06-07 16:19:11.417 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:19:11.432 [info] > git status -z -uall [8ms]
2025-06-07 16:19:11.433 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:19:16.452 [info] > git config --get commit.template [6ms]
2025-06-07 16:19:16.453 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:19:16.484 [info] > git status -z -uall [25ms]
2025-06-07 16:19:16.485 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:19:21.516 [info] > git config --get commit.template [15ms]
2025-06-07 16:19:21.518 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:19:21.538 [info] > git status -z -uall [9ms]
2025-06-07 16:19:21.541 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:19:26.569 [info] > git config --get commit.template [11ms]
2025-06-07 16:19:26.572 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:19:26.599 [info] > git status -z -uall [15ms]
2025-06-07 16:19:26.600 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:19:31.633 [info] > git config --get commit.template [15ms]
2025-06-07 16:19:31.634 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:19:31.664 [info] > git status -z -uall [15ms]
2025-06-07 16:19:31.665 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:19:36.716 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:19:36.716 [info] > git config --get commit.template [29ms]
2025-06-07 16:19:36.772 [info] > git status -z -uall [35ms]
2025-06-07 16:19:36.773 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:19:41.793 [info] > git config --get commit.template [5ms]
2025-06-07 16:19:41.794 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:19:41.806 [info] > git status -z -uall [6ms]
2025-06-07 16:19:41.807 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:19:46.822 [info] > git config --get commit.template [5ms]
2025-06-07 16:19:46.823 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:19:46.836 [info] > git status -z -uall [7ms]
2025-06-07 16:19:46.837 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:19:51.855 [info] > git config --get commit.template [2ms]
2025-06-07 16:19:51.865 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:19:51.893 [info] > git status -z -uall [13ms]
2025-06-07 16:19:51.894 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:19:56.918 [info] > git config --get commit.template [7ms]
2025-06-07 16:19:56.919 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:19:56.934 [info] > git status -z -uall [7ms]
2025-06-07 16:19:56.935 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:20:01.954 [info] > git config --get commit.template [6ms]
2025-06-07 16:20:01.955 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:20:01.966 [info] > git status -z -uall [5ms]
2025-06-07 16:20:01.967 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:20:06.986 [info] > git config --get commit.template [6ms]
2025-06-07 16:20:06.987 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:20:06.997 [info] > git status -z -uall [5ms]
2025-06-07 16:20:06.998 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:20:12.018 [info] > git config --get commit.template [8ms]
2025-06-07 16:20:12.024 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-07 16:20:12.039 [info] > git status -z -uall [6ms]
2025-06-07 16:20:12.040 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:20:17.058 [info] > git config --get commit.template [7ms]
2025-06-07 16:20:17.059 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:20:17.075 [info] > git status -z -uall [7ms]
2025-06-07 16:20:17.075 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:20:22.096 [info] > git config --get commit.template [7ms]
2025-06-07 16:20:22.097 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:20:22.111 [info] > git status -z -uall [7ms]
2025-06-07 16:20:22.112 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:20:27.129 [info] > git config --get commit.template [6ms]
2025-06-07 16:20:27.130 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:20:27.143 [info] > git status -z -uall [6ms]
2025-06-07 16:20:27.144 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:20:32.173 [info] > git config --get commit.template [6ms]
2025-06-07 16:20:32.188 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:20:32.202 [info] > git status -z -uall [6ms]
2025-06-07 16:20:32.203 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:20:37.225 [info] > git config --get commit.template [9ms]
2025-06-07 16:20:37.227 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:20:37.248 [info] > git status -z -uall [9ms]
2025-06-07 16:20:37.249 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:20:42.269 [info] > git config --get commit.template [6ms]
2025-06-07 16:20:42.270 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:20:42.283 [info] > git status -z -uall [6ms]
2025-06-07 16:20:42.284 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:20:47.301 [info] > git config --get commit.template [6ms]
2025-06-07 16:20:47.302 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:20:47.314 [info] > git status -z -uall [6ms]
2025-06-07 16:20:47.315 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:20:52.331 [info] > git config --get commit.template [5ms]
2025-06-07 16:20:52.332 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:20:52.344 [info] > git status -z -uall [6ms]
2025-06-07 16:20:52.345 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:20:57.389 [info] > git config --get commit.template [20ms]
2025-06-07 16:20:57.397 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-07 16:20:57.434 [info] > git status -z -uall [13ms]
2025-06-07 16:20:57.436 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:21:02.458 [info] > git config --get commit.template [7ms]
2025-06-07 16:21:02.459 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:21:02.473 [info] > git status -z -uall [7ms]
2025-06-07 16:21:02.474 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:21:07.494 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 16:21:07.495 [info] > git config --get commit.template [11ms]
2025-06-07 16:21:07.508 [info] > git status -z -uall [5ms]
2025-06-07 16:21:07.509 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:21:12.527 [info] > git config --get commit.template [7ms]
2025-06-07 16:21:12.528 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:21:12.541 [info] > git status -z -uall [7ms]
2025-06-07 16:21:12.542 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:21:17.557 [info] > git config --get commit.template [5ms]
2025-06-07 16:21:17.558 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:21:17.569 [info] > git status -z -uall [6ms]
2025-06-07 16:21:17.570 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:21:22.593 [info] > git config --get commit.template [6ms]
2025-06-07 16:21:22.594 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:21:22.606 [info] > git status -z -uall [6ms]
2025-06-07 16:21:22.607 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:21:27.624 [info] > git config --get commit.template [6ms]
2025-06-07 16:21:27.625 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:21:27.637 [info] > git status -z -uall [5ms]
2025-06-07 16:21:27.638 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:21:32.654 [info] > git config --get commit.template [5ms]
2025-06-07 16:21:32.656 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:21:32.668 [info] > git status -z -uall [6ms]
2025-06-07 16:21:32.669 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:21:37.688 [info] > git config --get commit.template [7ms]
2025-06-07 16:21:37.689 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:21:37.699 [info] > git status -z -uall [5ms]
2025-06-07 16:21:37.700 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:21:42.741 [info] > git config --get commit.template [18ms]
2025-06-07 16:21:42.741 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:21:42.765 [info] > git status -z -uall [12ms]
2025-06-07 16:21:42.765 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:21:47.786 [info] > git config --get commit.template [8ms]
2025-06-07 16:21:47.787 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:21:47.800 [info] > git status -z -uall [6ms]
2025-06-07 16:21:47.801 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:21:52.866 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-07 16:21:52.866 [info] > git config --get commit.template [25ms]
2025-06-07 16:21:52.886 [info] > git status -z -uall [8ms]
2025-06-07 16:21:52.888 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:21:57.916 [info] > git config --get commit.template [14ms]
2025-06-07 16:21:57.917 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:21:57.935 [info] > git status -z -uall [9ms]
2025-06-07 16:21:57.936 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:22:02.954 [info] > git config --get commit.template [6ms]
2025-06-07 16:22:02.956 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:22:02.976 [info] > git status -z -uall [9ms]
2025-06-07 16:22:02.977 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:22:07.998 [info] > git config --get commit.template [7ms]
2025-06-07 16:22:08.000 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:22:08.012 [info] > git status -z -uall [5ms]
2025-06-07 16:22:08.013 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:22:13.032 [info] > git config --get commit.template [7ms]
2025-06-07 16:22:13.033 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:22:13.044 [info] > git status -z -uall [5ms]
2025-06-07 16:22:13.045 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:22:18.081 [info] > git config --get commit.template [7ms]
2025-06-07 16:22:18.082 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:22:18.094 [info] > git status -z -uall [5ms]
2025-06-07 16:22:18.095 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:22:23.115 [info] > git config --get commit.template [8ms]
2025-06-07 16:22:23.116 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:22:23.131 [info] > git status -z -uall [6ms]
2025-06-07 16:22:23.132 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:22:28.156 [info] > git config --get commit.template [1ms]
2025-06-07 16:22:28.170 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:22:28.203 [info] > git status -z -uall [10ms]
2025-06-07 16:22:28.205 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:22:33.223 [info] > git config --get commit.template [7ms]
2025-06-07 16:22:33.225 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:22:33.237 [info] > git status -z -uall [6ms]
2025-06-07 16:22:33.239 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:22:38.260 [info] > git config --get commit.template [7ms]
2025-06-07 16:22:38.261 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:22:38.273 [info] > git status -z -uall [7ms]
2025-06-07 16:22:38.273 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:22:43.293 [info] > git config --get commit.template [7ms]
2025-06-07 16:22:43.295 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:22:43.307 [info] > git status -z -uall [7ms]
2025-06-07 16:22:43.308 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:22:48.336 [info] > git config --get commit.template [10ms]
2025-06-07 16:22:48.337 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:22:48.360 [info] > git status -z -uall [14ms]
2025-06-07 16:22:48.360 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:22:53.380 [info] > git config --get commit.template [8ms]
2025-06-07 16:22:53.381 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:22:53.402 [info] > git status -z -uall [9ms]
2025-06-07 16:22:53.403 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:22:58.416 [info] > git config --get commit.template [2ms]
2025-06-07 16:22:58.424 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:22:58.437 [info] > git status -z -uall [8ms]
2025-06-07 16:22:58.438 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:23:03.462 [info] > git config --get commit.template [9ms]
2025-06-07 16:23:03.463 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:23:03.487 [info] > git status -z -uall [13ms]
2025-06-07 16:23:03.489 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:23:08.526 [info] > git config --get commit.template [19ms]
2025-06-07 16:23:08.528 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:23:08.559 [info] > git status -z -uall [15ms]
2025-06-07 16:23:08.562 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:23:13.603 [info] > git config --get commit.template [20ms]
2025-06-07 16:23:13.604 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:23:13.654 [info] > git status -z -uall [23ms]
2025-06-07 16:23:13.655 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:23:18.676 [info] > git config --get commit.template [9ms]
2025-06-07 16:23:18.677 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:23:18.693 [info] > git status -z -uall [7ms]
2025-06-07 16:23:18.694 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:23:23.713 [info] > git config --get commit.template [7ms]
2025-06-07 16:23:23.714 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:23:23.727 [info] > git status -z -uall [6ms]
2025-06-07 16:23:23.728 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:23:28.817 [info] > git config --get commit.template [73ms]
2025-06-07 16:23:28.829 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:23:28.844 [info] > git status -z -uall [8ms]
2025-06-07 16:23:28.845 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:23:33.865 [info] > git config --get commit.template [7ms]
2025-06-07 16:23:33.866 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:23:33.879 [info] > git status -z -uall [7ms]
2025-06-07 16:23:33.880 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:23:38.899 [info] > git config --get commit.template [7ms]
2025-06-07 16:23:38.900 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:23:38.913 [info] > git status -z -uall [6ms]
2025-06-07 16:23:38.915 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:23:43.942 [info] > git config --get commit.template [14ms]
2025-06-07 16:23:43.942 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:23:43.971 [info] > git status -z -uall [10ms]
2025-06-07 16:23:43.972 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:23:49.000 [info] > git config --get commit.template [14ms]
2025-06-07 16:23:49.000 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:23:49.031 [info] > git status -z -uall [16ms]
2025-06-07 16:23:49.036 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:23:54.061 [info] > git config --get commit.template [9ms]
2025-06-07 16:23:54.062 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:23:54.086 [info] > git status -z -uall [12ms]
2025-06-07 16:23:54.088 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:23:59.182 [info] > git config --get commit.template [76ms]
2025-06-07 16:23:59.200 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:23:59.224 [info] > git status -z -uall [14ms]
2025-06-07 16:23:59.225 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:24:04.242 [info] > git config --get commit.template [1ms]
2025-06-07 16:24:04.254 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:24:04.269 [info] > git status -z -uall [7ms]
2025-06-07 16:24:04.271 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:24:09.292 [info] > git config --get commit.template [8ms]
2025-06-07 16:24:09.293 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:24:09.306 [info] > git status -z -uall [6ms]
2025-06-07 16:24:09.307 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:24:14.345 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-07 16:24:14.345 [info] > git config --get commit.template [23ms]
2025-06-07 16:24:14.371 [info] > git status -z -uall [12ms]
2025-06-07 16:24:14.371 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:24:19.389 [info] > git config --get commit.template [6ms]
2025-06-07 16:24:19.391 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:24:19.407 [info] > git status -z -uall [8ms]
2025-06-07 16:24:19.407 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:24:24.425 [info] > git config --get commit.template [6ms]
2025-06-07 16:24:24.426 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:24:24.437 [info] > git status -z -uall [5ms]
2025-06-07 16:24:24.438 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:24:29.466 [info] > git config --get commit.template [12ms]
2025-06-07 16:24:29.468 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:24:29.487 [info] > git status -z -uall [11ms]
2025-06-07 16:24:29.489 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:24:34.508 [info] > git config --get commit.template [2ms]
2025-06-07 16:24:34.521 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:24:34.540 [info] > git status -z -uall [8ms]
2025-06-07 16:24:34.541 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:24:39.557 [info] > git config --get commit.template [5ms]
2025-06-07 16:24:39.558 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:24:39.570 [info] > git status -z -uall [5ms]
2025-06-07 16:24:39.571 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:24:44.587 [info] > git config --get commit.template [6ms]
2025-06-07 16:24:44.588 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:24:44.600 [info] > git status -z -uall [6ms]
2025-06-07 16:24:44.601 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:24:49.623 [info] > git config --get commit.template [8ms]
2025-06-07 16:24:49.624 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:24:49.636 [info] > git status -z -uall [6ms]
2025-06-07 16:24:49.637 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:24:54.655 [info] > git config --get commit.template [6ms]
2025-06-07 16:24:54.656 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:24:54.671 [info] > git status -z -uall [7ms]
2025-06-07 16:24:54.672 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:24:59.689 [info] > git config --get commit.template [6ms]
2025-06-07 16:24:59.690 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:24:59.702 [info] > git status -z -uall [5ms]
2025-06-07 16:24:59.702 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:25:04.721 [info] > git config --get commit.template [7ms]
2025-06-07 16:25:04.723 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:25:04.735 [info] > git status -z -uall [6ms]
2025-06-07 16:25:04.736 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:25:09.753 [info] > git config --get commit.template [7ms]
2025-06-07 16:25:09.754 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:25:09.766 [info] > git status -z -uall [6ms]
2025-06-07 16:25:09.766 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:25:14.791 [info] > git config --get commit.template [10ms]
2025-06-07 16:25:14.792 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:25:14.806 [info] > git status -z -uall [7ms]
2025-06-07 16:25:14.808 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:25:19.911 [info] > git config --get commit.template [33ms]
2025-06-07 16:25:19.914 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-06-07 16:25:19.955 [info] > git status -z -uall [18ms]
2025-06-07 16:25:19.957 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:25:24.989 [info] > git config --get commit.template [14ms]
2025-06-07 16:25:24.991 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:25:25.025 [info] > git status -z -uall [22ms]
2025-06-07 16:25:25.026 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-07 16:25:30.053 [info] > git config --get commit.template [10ms]
2025-06-07 16:25:30.054 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:25:30.086 [info] > git status -z -uall [18ms]
2025-06-07 16:25:30.087 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:25:35.110 [info] > git config --get commit.template [0ms]
2025-06-07 16:25:35.150 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 16:25:35.211 [info] > git status -z -uall [29ms]
2025-06-07 16:25:35.211 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:25:40.239 [info] > git config --get commit.template [9ms]
2025-06-07 16:25:40.240 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:25:40.262 [info] > git status -z -uall [12ms]
2025-06-07 16:25:40.263 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:25:45.282 [info] > git config --get commit.template [0ms]
2025-06-07 16:25:45.297 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:25:45.322 [info] > git status -z -uall [12ms]
2025-06-07 16:25:45.324 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:25:50.343 [info] > git config --get commit.template [5ms]
2025-06-07 16:25:50.344 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:25:50.358 [info] > git status -z -uall [8ms]
2025-06-07 16:25:50.358 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:25:57.442 [info] > git config --get commit.template [9ms]
2025-06-07 16:25:57.442 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:25:57.475 [info] > git status -z -uall [17ms]
2025-06-07 16:25:57.478 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:26:02.498 [info] > git config --get commit.template [7ms]
2025-06-07 16:26:02.499 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:26:02.510 [info] > git status -z -uall [5ms]
2025-06-07 16:26:02.511 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:26:08.893 [info] > git config --get commit.template [10ms]
2025-06-07 16:26:08.894 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:26:08.905 [info] > git status -z -uall [5ms]
2025-06-07 16:26:08.907 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:26:13.928 [info] > git config --get commit.template [1ms]
2025-06-07 16:26:13.944 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:26:13.966 [info] > git status -z -uall [8ms]
2025-06-07 16:26:13.967 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:26:20.043 [info] > git config --get commit.template [7ms]
2025-06-07 16:26:20.044 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:26:20.056 [info] > git status -z -uall [6ms]
2025-06-07 16:26:20.056 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:26:25.074 [info] > git config --get commit.template [7ms]
2025-06-07 16:26:25.075 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:26:25.089 [info] > git status -z -uall [7ms]
2025-06-07 16:26:25.089 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:26:30.111 [info] > git config --get commit.template [8ms]
2025-06-07 16:26:30.112 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:26:30.123 [info] > git status -z -uall [5ms]
2025-06-07 16:26:30.124 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:26:35.141 [info] > git config --get commit.template [5ms]
2025-06-07 16:26:35.142 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:26:35.158 [info] > git status -z -uall [9ms]
2025-06-07 16:26:35.159 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:26:40.177 [info] > git config --get commit.template [7ms]
2025-06-07 16:26:40.178 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:26:40.192 [info] > git status -z -uall [7ms]
2025-06-07 16:26:40.192 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:27:40.019 [info] > git config --get commit.template [8ms]
2025-06-07 16:27:40.021 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:27:40.036 [info] > git status -z -uall [7ms]
2025-06-07 16:27:40.037 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:27:45.059 [info] > git config --get commit.template [9ms]
2025-06-07 16:27:45.060 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:27:45.090 [info] > git status -z -uall [13ms]
2025-06-07 16:27:45.091 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:27:50.119 [info] > git config --get commit.template [13ms]
2025-06-07 16:27:50.121 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:27:50.141 [info] > git status -z -uall [9ms]
2025-06-07 16:27:50.143 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:27:55.166 [info] > git config --get commit.template [8ms]
2025-06-07 16:27:55.168 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:27:55.183 [info] > git status -z -uall [8ms]
2025-06-07 16:27:55.184 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:28:00.201 [info] > git config --get commit.template [7ms]
2025-06-07 16:28:00.202 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:28:00.215 [info] > git status -z -uall [7ms]
2025-06-07 16:28:00.216 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:28:05.243 [info] > git config --get commit.template [12ms]
2025-06-07 16:28:05.251 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 16:28:05.288 [info] > git status -z -uall [19ms]
2025-06-07 16:28:05.290 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 16:28:19.668 [info] > git config --get commit.template [5ms]
2025-06-07 16:28:19.669 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:28:19.681 [info] > git status -z -uall [5ms]
2025-06-07 16:28:19.683 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:28:24.700 [info] > git config --get commit.template [5ms]
2025-06-07 16:28:24.701 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:28:24.712 [info] > git status -z -uall [5ms]
2025-06-07 16:28:24.716 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:28:44.433 [info] > git config --get commit.template [6ms]
2025-06-07 16:28:44.435 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:28:44.446 [info] > git status -z -uall [6ms]
2025-06-07 16:28:44.447 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:28:44.458 [info] > git merge-base refs/heads/main refs/remotes/origin/main [1ms]
2025-06-07 16:28:44.467 [info] > git diff --name-status -z --diff-filter=ADMR afcc68bbfc7a9840fe8bc9258ce0aab95444c453...refs/remotes/origin/main [2ms]
2025-06-07 16:28:49.472 [info] > git config --get commit.template [1ms]
2025-06-07 16:28:49.483 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:28:49.505 [info] > git status -z -uall [11ms]
2025-06-07 16:28:49.505 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:28:54.522 [info] > git config --get commit.template [6ms]
2025-06-07 16:28:54.523 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:28:54.538 [info] > git status -z -uall [6ms]
2025-06-07 16:28:54.540 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:28:59.560 [info] > git config --get commit.template [7ms]
2025-06-07 16:28:59.561 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:28:59.574 [info] > git status -z -uall [7ms]
2025-06-07 16:28:59.575 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:29:07.155 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:29:07.155 [info] > git config --get commit.template [16ms]
2025-06-07 16:29:07.178 [info] > git status -z -uall [11ms]
2025-06-07 16:29:07.179 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:29:16.148 [info] > git config --get commit.template [9ms]
2025-06-07 16:29:16.149 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:29:16.163 [info] > git status -z -uall [7ms]
2025-06-07 16:29:16.165 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:29:21.188 [info] > git config --get commit.template [4ms]
2025-06-07 16:29:21.201 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:29:21.222 [info] > git status -z -uall [10ms]
2025-06-07 16:29:21.223 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:29:26.249 [info] > git config --get commit.template [8ms]
2025-06-07 16:29:26.250 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:29:26.264 [info] > git status -z -uall [7ms]
2025-06-07 16:29:26.265 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:29:31.289 [info] > git config --get commit.template [11ms]
2025-06-07 16:29:31.290 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:29:31.307 [info] > git status -z -uall [8ms]
2025-06-07 16:29:31.309 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:29:36.329 [info] > git config --get commit.template [6ms]
2025-06-07 16:29:36.331 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:29:36.356 [info] > git status -z -uall [12ms]
2025-06-07 16:29:36.356 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:29:41.374 [info] > git config --get commit.template [6ms]
2025-06-07 16:29:41.375 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:29:41.388 [info] > git status -z -uall [6ms]
2025-06-07 16:29:41.389 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:29:46.415 [info] > git config --get commit.template [8ms]
2025-06-07 16:29:46.416 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:29:46.426 [info] > git status -z -uall [5ms]
2025-06-07 16:29:46.428 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:29:51.449 [info] > git config --get commit.template [9ms]
2025-06-07 16:29:51.451 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:29:51.465 [info] > git status -z -uall [6ms]
2025-06-07 16:29:51.466 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:29:56.486 [info] > git config --get commit.template [2ms]
2025-06-07 16:29:56.556 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [49ms]
2025-06-07 16:29:56.591 [info] > git status -z -uall [15ms]
2025-06-07 16:29:56.592 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:30:01.613 [info] > git config --get commit.template [7ms]
2025-06-07 16:30:01.614 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:30:01.628 [info] > git status -z -uall [8ms]
2025-06-07 16:30:01.629 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:30:06.650 [info] > git config --get commit.template [7ms]
2025-06-07 16:30:06.652 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:30:06.663 [info] > git status -z -uall [5ms]
2025-06-07 16:30:06.664 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:30:11.688 [info] > git config --get commit.template [4ms]
2025-06-07 16:30:11.702 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:30:11.722 [info] > git status -z -uall [11ms]
2025-06-07 16:30:11.725 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:30:16.745 [info] > git config --get commit.template [6ms]
2025-06-07 16:30:16.748 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:30:16.786 [info] > git status -z -uall [10ms]
2025-06-07 16:30:16.787 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:30:21.818 [info] > git config --get commit.template [16ms]
2025-06-07 16:30:21.819 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:30:21.838 [info] > git status -z -uall [9ms]
2025-06-07 16:30:21.839 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:30:26.861 [info] > git config --get commit.template [7ms]
2025-06-07 16:30:26.862 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:30:26.880 [info] > git status -z -uall [11ms]
2025-06-07 16:30:26.883 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:30:31.937 [info] > git config --get commit.template [6ms]
2025-06-07 16:30:31.938 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:30:31.952 [info] > git status -z -uall [8ms]
2025-06-07 16:30:31.952 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:30:36.977 [info] > git config --get commit.template [11ms]
2025-06-07 16:30:36.979 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:30:36.995 [info] > git status -z -uall [7ms]
2025-06-07 16:30:36.996 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:30:42.030 [info] > git config --get commit.template [12ms]
2025-06-07 16:30:42.031 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:30:42.077 [info] > git status -z -uall [21ms]
2025-06-07 16:30:42.077 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:30:47.096 [info] > git config --get commit.template [5ms]
2025-06-07 16:30:47.097 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:30:47.174 [info] > git status -z -uall [67ms]
2025-06-07 16:30:47.177 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [59ms]
2025-06-07 16:30:52.192 [info] > git config --get commit.template [5ms]
2025-06-07 16:30:52.193 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:30:52.204 [info] > git status -z -uall [5ms]
2025-06-07 16:30:52.205 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:30:57.248 [info] > git config --get commit.template [19ms]
2025-06-07 16:30:57.249 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:30:57.291 [info] > git status -z -uall [16ms]
2025-06-07 16:30:57.294 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:31:02.323 [info] > git config --get commit.template [8ms]
2025-06-07 16:31:02.323 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:31:02.349 [info] > git status -z -uall [10ms]
2025-06-07 16:31:02.350 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:31:07.380 [info] > git config --get commit.template [13ms]
2025-06-07 16:31:07.383 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:31:07.434 [info] > git status -z -uall [34ms]
2025-06-07 16:31:07.440 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-07 16:31:12.461 [info] > git config --get commit.template [6ms]
2025-06-07 16:31:12.462 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:31:12.473 [info] > git status -z -uall [5ms]
2025-06-07 16:31:12.473 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:31:17.492 [info] > git config --get commit.template [1ms]
2025-06-07 16:31:17.511 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-07 16:31:17.526 [info] > git status -z -uall [7ms]
2025-06-07 16:31:17.527 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:31:22.545 [info] > git config --get commit.template [6ms]
2025-06-07 16:31:22.548 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:31:22.569 [info] > git status -z -uall [13ms]
2025-06-07 16:31:22.570 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:31:27.588 [info] > git config --get commit.template [2ms]
2025-06-07 16:31:27.602 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:31:27.631 [info] > git status -z -uall [13ms]
2025-06-07 16:31:27.632 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:31:32.646 [info] > git config --get commit.template [2ms]
2025-06-07 16:31:32.654 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:31:32.666 [info] > git status -z -uall [6ms]
2025-06-07 16:31:32.667 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:31:37.729 [info] > git config --get commit.template [17ms]
2025-06-07 16:31:37.732 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:31:37.769 [info] > git status -z -uall [17ms]
2025-06-07 16:31:37.770 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:31:42.858 [info] > git config --get commit.template [40ms]
2025-06-07 16:31:42.861 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:31:42.916 [info] > git status -z -uall [22ms]
2025-06-07 16:31:42.920 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:31:47.941 [info] > git config --get commit.template [1ms]
2025-06-07 16:31:47.956 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:31:47.977 [info] > git status -z -uall [11ms]
2025-06-07 16:31:47.979 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:31:53.019 [info] > git config --get commit.template [25ms]
2025-06-07 16:31:53.021 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:31:53.069 [info] > git status -z -uall [24ms]
2025-06-07 16:31:53.070 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:31:58.091 [info] > git config --get commit.template [9ms]
2025-06-07 16:31:58.092 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:31:58.103 [info] > git status -z -uall [6ms]
2025-06-07 16:31:58.104 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:33:38.205 [info] > git config --get commit.template [10ms]
2025-06-07 16:33:38.206 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:33:38.234 [info] > git status -z -uall [17ms]
2025-06-07 16:33:38.235 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:33:50.309 [info] > git config --get commit.template [11ms]
2025-06-07 16:33:50.310 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:33:50.338 [info] > git status -z -uall [15ms]
2025-06-07 16:33:50.339 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:33:55.358 [info] > git config --get commit.template [8ms]
2025-06-07 16:33:55.359 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:33:55.373 [info] > git status -z -uall [7ms]
2025-06-07 16:33:55.374 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:35:09.036 [info] > git config --get commit.template [2ms]
2025-06-07 16:35:09.045 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:35:09.060 [info] > git status -z -uall [8ms]
2025-06-07 16:35:09.061 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:35:14.078 [info] > git config --get commit.template [3ms]
2025-06-07 16:35:14.097 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:35:14.115 [info] > git status -z -uall [9ms]
2025-06-07 16:35:14.116 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:35:19.134 [info] > git config --get commit.template [6ms]
2025-06-07 16:35:19.142 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 16:35:19.158 [info] > git status -z -uall [6ms]
2025-06-07 16:35:19.159 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:35:24.180 [info] > git config --get commit.template [7ms]
2025-06-07 16:35:24.184 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:35:24.200 [info] > git status -z -uall [7ms]
2025-06-07 16:35:24.201 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:35:29.244 [info] > git config --get commit.template [6ms]
2025-06-07 16:35:29.245 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:35:29.261 [info] > git status -z -uall [10ms]
2025-06-07 16:35:29.263 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 16:35:34.281 [info] > git config --get commit.template [6ms]
2025-06-07 16:35:34.282 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:35:34.293 [info] > git status -z -uall [6ms]
2025-06-07 16:35:34.293 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:35:39.317 [info] > git config --get commit.template [10ms]
2025-06-07 16:35:39.318 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:35:39.333 [info] > git status -z -uall [6ms]
2025-06-07 16:35:39.335 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:35:44.359 [info] > git config --get commit.template [9ms]
2025-06-07 16:35:44.360 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:35:44.379 [info] > git status -z -uall [10ms]
2025-06-07 16:35:44.453 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [75ms]
2025-06-07 16:36:29.507 [info] > git config --get commit.template [3ms]
2025-06-07 16:36:29.534 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-07 16:36:29.588 [info] > git status -z -uall [29ms]
2025-06-07 16:36:29.589 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-07 16:36:34.611 [info] > git config --get commit.template [9ms]
2025-06-07 16:36:34.613 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:36:34.631 [info] > git status -z -uall [8ms]
2025-06-07 16:36:34.632 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:36:39.654 [info] > git config --get commit.template [9ms]
2025-06-07 16:36:39.656 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:36:39.669 [info] > git status -z -uall [6ms]
2025-06-07 16:36:39.670 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:36:44.690 [info] > git config --get commit.template [6ms]
2025-06-07 16:36:44.692 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:36:44.705 [info] > git status -z -uall [7ms]
2025-06-07 16:36:44.706 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:36:49.736 [info] > git config --get commit.template [14ms]
2025-06-07 16:36:49.738 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:36:49.757 [info] > git status -z -uall [9ms]
2025-06-07 16:36:49.757 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:36:54.791 [info] > git config --get commit.template [6ms]
2025-06-07 16:36:54.818 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-07 16:36:54.843 [info] > git status -z -uall [12ms]
2025-06-07 16:36:54.844 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:36:59.870 [info] > git config --get commit.template [2ms]
2025-06-07 16:36:59.891 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:36:59.967 [info] > git status -z -uall [50ms]
2025-06-07 16:36:59.969 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:37:05.106 [info] > git config --get commit.template [33ms]
2025-06-07 16:37:05.111 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 16:37:05.165 [info] > git status -z -uall [24ms]
2025-06-07 16:37:05.169 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:37:10.293 [info] > git config --get commit.template [111ms]
2025-06-07 16:37:10.294 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [85ms]
2025-06-07 16:37:10.324 [info] > git status -z -uall [14ms]
2025-06-07 16:37:10.325 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:37:16.494 [info] > git config --get commit.template [12ms]
2025-06-07 16:37:16.497 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:37:16.531 [info] > git status -z -uall [22ms]
2025-06-07 16:37:16.531 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:37:21.557 [info] > git config --get commit.template [8ms]
2025-06-07 16:37:21.560 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:37:21.575 [info] > git status -z -uall [8ms]
2025-06-07 16:37:21.576 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:37:26.616 [info] > git config --get commit.template [19ms]
2025-06-07 16:37:26.617 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:37:26.657 [info] > git status -z -uall [16ms]
2025-06-07 16:37:26.660 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:37:31.685 [info] > git config --get commit.template [10ms]
2025-06-07 16:37:31.686 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:37:31.719 [info] > git status -z -uall [20ms]
2025-06-07 16:37:31.720 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:37:36.743 [info] > git config --get commit.template [2ms]
2025-06-07 16:37:36.763 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:37:36.797 [info] > git status -z -uall [22ms]
2025-06-07 16:37:36.800 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:37:41.823 [info] > git config --get commit.template [1ms]
2025-06-07 16:37:41.831 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:37:41.845 [info] > git status -z -uall [8ms]
2025-06-07 16:37:41.846 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:37:46.884 [info] > git config --get commit.template [16ms]
2025-06-07 16:37:46.886 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:37:46.908 [info] > git status -z -uall [10ms]
2025-06-07 16:37:46.909 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:42:06.019 [info] > git config --get commit.template [17ms]
2025-06-07 16:42:06.022 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:42:06.056 [info] > git status -z -uall [17ms]
2025-06-07 16:42:06.057 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:42:11.096 [info] > git config --get commit.template [3ms]
2025-06-07 16:42:11.112 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:42:11.141 [info] > git status -z -uall [15ms]
2025-06-07 16:42:11.142 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:42:16.182 [info] > git config --get commit.template [20ms]
2025-06-07 16:42:16.185 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:42:16.230 [info] > git status -z -uall [18ms]
2025-06-07 16:42:16.234 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:42:21.267 [info] > git config --get commit.template [14ms]
2025-06-07 16:42:21.269 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:42:21.293 [info] > git status -z -uall [11ms]
2025-06-07 16:42:21.295 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:42:26.312 [info] > git config --get commit.template [4ms]
2025-06-07 16:42:26.326 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:42:26.349 [info] > git status -z -uall [11ms]
2025-06-07 16:42:26.350 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:42:31.368 [info] > git config --get commit.template [6ms]
2025-06-07 16:42:31.369 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:42:31.382 [info] > git status -z -uall [6ms]
2025-06-07 16:42:31.383 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:42:36.406 [info] > git config --get commit.template [9ms]
2025-06-07 16:42:36.407 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:42:36.422 [info] > git status -z -uall [7ms]
2025-06-07 16:42:36.423 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:42:41.440 [info] > git config --get commit.template [2ms]
2025-06-07 16:42:41.453 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:42:41.466 [info] > git status -z -uall [6ms]
2025-06-07 16:42:41.467 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:42:46.485 [info] > git config --get commit.template [5ms]
2025-06-07 16:42:46.485 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:42:46.501 [info] > git status -z -uall [9ms]
2025-06-07 16:42:46.502 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:42:51.521 [info] > git config --get commit.template [6ms]
2025-06-07 16:42:51.522 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:42:51.536 [info] > git status -z -uall [7ms]
2025-06-07 16:42:51.538 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:42:56.558 [info] > git config --get commit.template [8ms]
2025-06-07 16:42:56.559 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:42:56.571 [info] > git status -z -uall [6ms]
2025-06-07 16:42:56.572 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:02:31.113 [info] > git config --get commit.template [5ms]
2025-06-07 17:02:31.114 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:02:31.129 [info] > git status -z -uall [8ms]
2025-06-07 17:02:31.130 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:03:01.444 [info] > git config --get commit.template [6ms]
2025-06-07 17:03:01.445 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:03:01.460 [info] > git status -z -uall [9ms]
2025-06-07 17:03:01.461 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:03:06.481 [info] > git config --get commit.template [7ms]
2025-06-07 17:03:06.483 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:03:06.498 [info] > git status -z -uall [6ms]
2025-06-07 17:03:06.499 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:03:11.518 [info] > git config --get commit.template [7ms]
2025-06-07 17:03:11.519 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:03:11.533 [info] > git status -z -uall [7ms]
2025-06-07 17:03:11.534 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:03:16.559 [info] > git config --get commit.template [11ms]
2025-06-07 17:03:16.561 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:03:16.582 [info] > git status -z -uall [11ms]
2025-06-07 17:03:16.583 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:03:21.601 [info] > git config --get commit.template [6ms]
2025-06-07 17:03:21.602 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:03:21.613 [info] > git status -z -uall [6ms]
2025-06-07 17:03:21.614 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:03:26.653 [info] > git config --get commit.template [16ms]
2025-06-07 17:03:26.654 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:03:26.678 [info] > git status -z -uall [12ms]
2025-06-07 17:03:26.681 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:03:31.719 [info] > git config --get commit.template [18ms]
2025-06-07 17:03:31.721 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:03:31.753 [info] > git status -z -uall [16ms]
2025-06-07 17:03:31.754 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:11:51.538 [info] > git config --get commit.template [6ms]
2025-06-07 17:11:51.539 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:11:51.557 [info] > git status -z -uall [8ms]
2025-06-07 17:11:51.558 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:11:56.573 [info] > git config --get commit.template [6ms]
2025-06-07 17:11:56.575 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:11:56.588 [info] > git status -z -uall [7ms]
2025-06-07 17:11:56.590 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:12:01.607 [info] > git config --get commit.template [6ms]
2025-06-07 17:12:01.608 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:12:01.620 [info] > git status -z -uall [6ms]
2025-06-07 17:12:01.621 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:12:06.644 [info] > git config --get commit.template [7ms]
2025-06-07 17:12:06.645 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:12:06.665 [info] > git status -z -uall [10ms]
2025-06-07 17:12:06.666 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:12:11.693 [info] > git config --get commit.template [1ms]
2025-06-07 17:12:11.712 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 17:12:11.745 [info] > git status -z -uall [14ms]
2025-06-07 17:12:11.746 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:12:16.778 [info] > git config --get commit.template [11ms]
2025-06-07 17:12:16.779 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:12:16.808 [info] > git status -z -uall [15ms]
2025-06-07 17:12:16.810 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:12:21.828 [info] > git config --get commit.template [1ms]
2025-06-07 17:12:21.847 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:12:21.865 [info] > git status -z -uall [6ms]
2025-06-07 17:12:21.866 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:12:26.898 [info] > git config --get commit.template [8ms]
2025-06-07 17:12:26.919 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:12:26.952 [info] > git status -z -uall [16ms]
2025-06-07 17:12:26.956 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:12:31.979 [info] > git config --get commit.template [8ms]
2025-06-07 17:12:31.980 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:12:31.991 [info] > git status -z -uall [6ms]
2025-06-07 17:12:31.991 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:12:37.018 [info] > git config --get commit.template [11ms]
2025-06-07 17:12:37.019 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:12:37.043 [info] > git status -z -uall [12ms]
2025-06-07 17:12:37.044 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:12:42.071 [info] > git config --get commit.template [14ms]
2025-06-07 17:12:42.073 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:12:42.104 [info] > git status -z -uall [19ms]
2025-06-07 17:12:42.105 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:12:47.905 [info] > git config --get commit.template [10ms]
2025-06-07 17:12:47.907 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:12:47.925 [info] > git status -z -uall [11ms]
2025-06-07 17:12:47.927 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:12:52.942 [info] > git config --get commit.template [1ms]
2025-06-07 17:12:52.952 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:12:52.968 [info] > git status -z -uall [8ms]
2025-06-07 17:12:52.970 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:12:57.993 [info] > git config --get commit.template [7ms]
2025-06-07 17:12:57.993 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:12:58.006 [info] > git status -z -uall [6ms]
2025-06-07 17:12:58.007 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:21:53.667 [info] > git config --get commit.template [17ms]
2025-06-07 17:21:53.668 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:21:53.711 [info] > git status -z -uall [22ms]
2025-06-07 17:21:53.712 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:22:12.433 [info] > git config --get commit.template [6ms]
2025-06-07 17:22:12.434 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:22:12.446 [info] > git status -z -uall [6ms]
2025-06-07 17:22:12.447 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:22:17.502 [info] > git config --get commit.template [8ms]
2025-06-07 17:22:17.504 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:22:17.518 [info] > git status -z -uall [9ms]
2025-06-07 17:22:17.518 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:22:22.540 [info] > git config --get commit.template [9ms]
2025-06-07 17:22:22.542 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:22:22.562 [info] > git status -z -uall [10ms]
2025-06-07 17:22:22.565 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:22:27.607 [info] > git config --get commit.template [11ms]
2025-06-07 17:22:27.608 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:22:27.630 [info] > git status -z -uall [10ms]
2025-06-07 17:22:27.632 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:22:32.653 [info] > git config --get commit.template [6ms]
2025-06-07 17:22:32.654 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:22:32.666 [info] > git status -z -uall [6ms]
2025-06-07 17:22:32.667 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:22:37.685 [info] > git config --get commit.template [5ms]
2025-06-07 17:22:37.686 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:22:37.697 [info] > git status -z -uall [5ms]
2025-06-07 17:22:37.698 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:26:08.265 [info] > git config --get commit.template [6ms]
2025-06-07 17:26:08.267 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:26:08.284 [info] > git status -z -uall [6ms]
2025-06-07 17:26:08.285 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:26:13.302 [info] > git config --get commit.template [6ms]
2025-06-07 17:26:13.304 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:26:13.314 [info] > git status -z -uall [5ms]
2025-06-07 17:26:13.316 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:26:18.330 [info] > git config --get commit.template [1ms]
2025-06-07 17:26:18.339 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:26:18.350 [info] > git status -z -uall [5ms]
2025-06-07 17:26:18.352 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:26:23.371 [info] > git config --get commit.template [5ms]
2025-06-07 17:26:23.372 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:26:23.386 [info] > git status -z -uall [8ms]
2025-06-07 17:26:23.386 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:26:28.404 [info] > git config --get commit.template [6ms]
2025-06-07 17:26:28.405 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:26:28.428 [info] > git status -z -uall [8ms]
2025-06-07 17:26:28.429 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:26:33.450 [info] > git config --get commit.template [7ms]
2025-06-07 17:26:33.451 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:26:33.462 [info] > git status -z -uall [5ms]
2025-06-07 17:26:33.464 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:26:38.523 [info] > git config --get commit.template [2ms]
2025-06-07 17:26:38.530 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:26:38.544 [info] > git status -z -uall [9ms]
2025-06-07 17:26:38.545 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:26:43.569 [info] > git config --get commit.template [11ms]
2025-06-07 17:26:43.573 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 17:26:43.589 [info] > git status -z -uall [6ms]
2025-06-07 17:26:43.590 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:26:47.063 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-07 17:26:48.611 [info] > git config --get commit.template [9ms]
2025-06-07 17:26:48.612 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:26:48.624 [info] > git status -z -uall [5ms]
2025-06-07 17:26:48.625 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:26:53.719 [info] > git config --get commit.template [77ms]
2025-06-07 17:26:53.720 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [59ms]
2025-06-07 17:26:53.740 [info] > git status -z -uall [8ms]
2025-06-07 17:26:53.741 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:26:58.163 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-07 17:26:58.765 [info] > git config --get commit.template [11ms]
2025-06-07 17:26:58.765 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:26:58.798 [info] > git status -z -uall [25ms]
2025-06-07 17:26:58.800 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:27:06.216 [info] > git config --get commit.template [7ms]
2025-06-07 17:27:06.217 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:27:06.227 [info] > git status -z -uall [5ms]
2025-06-07 17:27:06.228 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:27:11.244 [info] > git config --get commit.template [5ms]
2025-06-07 17:27:11.246 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:27:11.260 [info] > git status -z -uall [6ms]
2025-06-07 17:27:11.261 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:27:16.278 [info] > git config --get commit.template [6ms]
2025-06-07 17:27:16.279 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:27:16.290 [info] > git status -z -uall [5ms]
2025-06-07 17:27:16.291 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:27:21.308 [info] > git config --get commit.template [7ms]
2025-06-07 17:27:21.309 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:27:21.323 [info] > git status -z -uall [6ms]
2025-06-07 17:27:21.323 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:27:21.890 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-07 17:27:23.090 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-07 17:27:24.097 [info] > git show --textconv :server/routes.ts [8ms]
2025-06-07 17:27:24.098 [info] > git ls-files --stage -- server/routes.ts [2ms]
2025-06-07 17:27:24.106 [info] > git cat-file -s b93bb47e551bcc9e5d022c81e3eccb295c6af454 [2ms]
2025-06-07 17:27:26.343 [info] > git config --get commit.template [7ms]
2025-06-07 17:27:26.344 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:27:26.356 [info] > git status -z -uall [5ms]
2025-06-07 17:27:26.357 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:27:31.389 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-07 17:27:31.389 [info] > git config --get commit.template [18ms]
2025-06-07 17:27:31.404 [info] > git status -z -uall [6ms]
2025-06-07 17:27:31.405 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:27:36.034 [info] > git blame --root --incremental bbfb3d7a212ab0362673f8b8544b7be7984d1e48 -- server/routes.ts [150ms]
2025-06-07 17:27:36.426 [info] > git config --get commit.template [9ms]
2025-06-07 17:27:36.426 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:27:36.439 [info] > git status -z -uall [7ms]
2025-06-07 17:27:36.440 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:27:41.472 [info] > git config --get commit.template [14ms]
2025-06-07 17:27:41.472 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:27:41.562 [info] > git status -z -uall [78ms]
2025-06-07 17:27:41.565 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [69ms]
2025-06-07 17:27:46.585 [info] > git config --get commit.template [8ms]
2025-06-07 17:27:46.586 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:27:46.602 [info] > git status -z -uall [7ms]
2025-06-07 17:27:46.603 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:27:51.624 [info] > git config --get commit.template [7ms]
2025-06-07 17:27:51.626 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:27:51.640 [info] > git status -z -uall [7ms]
2025-06-07 17:27:51.641 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:27:56.662 [info] > git config --get commit.template [8ms]
2025-06-07 17:27:56.664 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:27:56.675 [info] > git status -z -uall [6ms]
2025-06-07 17:27:56.676 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:28:01.694 [info] > git config --get commit.template [6ms]
2025-06-07 17:28:01.695 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:28:01.706 [info] > git status -z -uall [5ms]
2025-06-07 17:28:01.708 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:28:06.739 [info] > git config --get commit.template [14ms]
2025-06-07 17:28:06.740 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:28:06.764 [info] > git status -z -uall [11ms]
2025-06-07 17:28:06.766 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:28:11.831 [info] > git config --get commit.template [53ms]
2025-06-07 17:28:11.846 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:28:11.865 [info] > git status -z -uall [10ms]
2025-06-07 17:28:11.867 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 17:28:16.884 [info] > git config --get commit.template [6ms]
2025-06-07 17:28:16.886 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:28:16.906 [info] > git status -z -uall [14ms]
2025-06-07 17:28:16.906 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-07 17:28:21.922 [info] > git config --get commit.template [1ms]
2025-06-07 17:28:21.934 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:28:22.002 [info] > git status -z -uall [61ms]
2025-06-07 17:28:22.002 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [56ms]
2025-06-07 17:28:27.022 [info] > git config --get commit.template [1ms]
2025-06-07 17:28:27.039 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:28:27.059 [info] > git status -z -uall [9ms]
2025-06-07 17:28:27.060 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:28:32.085 [info] > git config --get commit.template [11ms]
2025-06-07 17:28:32.087 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:28:32.103 [info] > git status -z -uall [8ms]
2025-06-07 17:28:32.104 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:28:37.127 [info] > git config --get commit.template [10ms]
2025-06-07 17:28:37.129 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:28:37.147 [info] > git status -z -uall [8ms]
2025-06-07 17:28:37.148 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:28:42.169 [info] > git config --get commit.template [7ms]
2025-06-07 17:28:42.171 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:28:42.185 [info] > git status -z -uall [6ms]
2025-06-07 17:28:42.186 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:28:47.217 [info] > git config --get commit.template [18ms]
2025-06-07 17:28:47.221 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 17:28:47.257 [info] > git status -z -uall [10ms]
2025-06-07 17:28:47.266 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-07 17:28:52.290 [info] > git config --get commit.template [6ms]
2025-06-07 17:28:52.292 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:28:52.308 [info] > git status -z -uall [8ms]
2025-06-07 17:28:52.309 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:28:57.328 [info] > git config --get commit.template [2ms]
2025-06-07 17:28:57.345 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:28:57.388 [info] > git status -z -uall [17ms]
2025-06-07 17:28:57.390 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:29:02.419 [info] > git config --get commit.template [11ms]
2025-06-07 17:29:02.421 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:29:02.441 [info] > git status -z -uall [11ms]
2025-06-07 17:29:02.442 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:29:07.463 [info] > git config --get commit.template [0ms]
2025-06-07 17:29:07.475 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:29:07.495 [info] > git status -z -uall [9ms]
2025-06-07 17:29:07.496 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:29:12.532 [info] > git config --get commit.template [19ms]
2025-06-07 17:29:12.533 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:29:12.559 [info] > git status -z -uall [14ms]
2025-06-07 17:29:12.560 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:29:17.616 [info] > git config --get commit.template [8ms]
2025-06-07 17:29:17.617 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:29:17.630 [info] > git status -z -uall [8ms]
2025-06-07 17:29:17.631 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:29:25.539 [info] > git config --get commit.template [19ms]
2025-06-07 17:29:25.563 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 17:29:25.637 [info] > git status -z -uall [45ms]
2025-06-07 17:29:25.639 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:29:30.661 [info] > git config --get commit.template [8ms]
2025-06-07 17:29:30.663 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:29:30.675 [info] > git status -z -uall [6ms]
2025-06-07 17:29:30.676 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:29:35.700 [info] > git config --get commit.template [0ms]
2025-06-07 17:29:35.715 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:29:35.760 [info] > git status -z -uall [21ms]
2025-06-07 17:29:35.764 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:29:40.782 [info] > git config --get commit.template [5ms]
2025-06-07 17:29:40.784 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:29:40.803 [info] > git status -z -uall [12ms]
2025-06-07 17:29:40.804 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:29:45.822 [info] > git config --get commit.template [6ms]
2025-06-07 17:29:45.823 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:29:45.836 [info] > git status -z -uall [6ms]
2025-06-07 17:29:45.837 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:29:50.853 [info] > git config --get commit.template [6ms]
2025-06-07 17:29:50.854 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:29:50.865 [info] > git status -z -uall [5ms]
2025-06-07 17:29:50.866 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:29:55.885 [info] > git config --get commit.template [7ms]
2025-06-07 17:29:55.886 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:29:55.898 [info] > git status -z -uall [6ms]
2025-06-07 17:29:55.899 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:30:00.948 [info] > git config --get commit.template [27ms]
2025-06-07 17:30:00.950 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:30:00.990 [info] > git status -z -uall [22ms]
2025-06-07 17:30:00.990 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:30:06.011 [info] > git config --get commit.template [3ms]
2025-06-07 17:30:06.022 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:30:06.036 [info] > git status -z -uall [6ms]
2025-06-07 17:30:06.037 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:30:11.058 [info] > git config --get commit.template [7ms]
2025-06-07 17:30:11.060 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:30:11.072 [info] > git status -z -uall [6ms]
2025-06-07 17:30:11.074 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:30:16.106 [info] > git config --get commit.template [15ms]
2025-06-07 17:30:16.109 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 17:30:16.134 [info] > git status -z -uall [16ms]
2025-06-07 17:30:16.138 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 17:30:21.160 [info] > git config --get commit.template [7ms]
2025-06-07 17:30:21.161 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:30:21.174 [info] > git status -z -uall [6ms]
2025-06-07 17:30:21.175 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:30:26.196 [info] > git config --get commit.template [8ms]
2025-06-07 17:30:26.197 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:30:26.222 [info] > git status -z -uall [13ms]
2025-06-07 17:30:26.225 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:30:31.246 [info] > git config --get commit.template [8ms]
2025-06-07 17:30:31.247 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:30:31.268 [info] > git status -z -uall [14ms]
2025-06-07 17:30:31.270 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:30:36.288 [info] > git config --get commit.template [2ms]
2025-06-07 17:30:36.309 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:30:36.338 [info] > git status -z -uall [13ms]
2025-06-07 17:30:36.339 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:30:41.359 [info] > git config --get commit.template [8ms]
2025-06-07 17:30:41.360 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:30:41.374 [info] > git status -z -uall [6ms]
2025-06-07 17:30:41.375 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:30:46.392 [info] > git config --get commit.template [5ms]
2025-06-07 17:30:46.393 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:30:46.405 [info] > git status -z -uall [6ms]
2025-06-07 17:30:46.406 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:30:51.422 [info] > git config --get commit.template [5ms]
2025-06-07 17:30:51.423 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:30:51.437 [info] > git status -z -uall [5ms]
2025-06-07 17:30:51.438 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:30:56.498 [info] > git config --get commit.template [1ms]
2025-06-07 17:30:56.506 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:30:56.517 [info] > git status -z -uall [6ms]
2025-06-07 17:30:56.518 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:31:01.548 [info] > git config --get commit.template [13ms]
2025-06-07 17:31:01.549 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:31:01.576 [info] > git status -z -uall [16ms]
2025-06-07 17:31:01.577 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:31:06.596 [info] > git config --get commit.template [7ms]
2025-06-07 17:31:06.597 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:31:06.610 [info] > git status -z -uall [6ms]
2025-06-07 17:31:06.611 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:31:11.626 [info] > git config --get commit.template [2ms]
2025-06-07 17:31:11.636 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:31:11.647 [info] > git status -z -uall [5ms]
2025-06-07 17:31:11.649 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:31:16.671 [info] > git config --get commit.template [8ms]
2025-06-07 17:31:16.672 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:31:16.689 [info] > git status -z -uall [10ms]
2025-06-07 17:31:16.689 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:31:21.732 [info] > git config --get commit.template [11ms]
2025-06-07 17:31:21.734 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:31:21.747 [info] > git status -z -uall [6ms]
2025-06-07 17:31:21.748 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:31:26.771 [info] > git config --get commit.template [6ms]
2025-06-07 17:31:26.772 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:31:26.784 [info] > git status -z -uall [6ms]
2025-06-07 17:31:26.785 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:31:31.803 [info] > git config --get commit.template [6ms]
2025-06-07 17:31:31.804 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:31:31.815 [info] > git status -z -uall [6ms]
2025-06-07 17:31:31.816 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:31:36.840 [info] > git config --get commit.template [9ms]
2025-06-07 17:31:36.841 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:31:36.854 [info] > git status -z -uall [6ms]
2025-06-07 17:31:36.855 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:31:41.873 [info] > git config --get commit.template [6ms]
2025-06-07 17:31:41.875 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:31:41.895 [info] > git status -z -uall [7ms]
2025-06-07 17:31:41.896 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:31:46.917 [info] > git config --get commit.template [7ms]
2025-06-07 17:31:46.918 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:31:46.931 [info] > git status -z -uall [7ms]
2025-06-07 17:31:46.932 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:31:51.955 [info] > git config --get commit.template [9ms]
2025-06-07 17:31:51.957 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:31:51.973 [info] > git status -z -uall [9ms]
2025-06-07 17:31:51.974 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:31:56.999 [info] > git config --get commit.template [10ms]
2025-06-07 17:31:57.000 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:31:57.017 [info] > git status -z -uall [9ms]
2025-06-07 17:31:57.018 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:32:02.040 [info] > git config --get commit.template [7ms]
2025-06-07 17:32:02.041 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:32:02.054 [info] > git status -z -uall [7ms]
2025-06-07 17:32:02.055 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:32:07.085 [info] > git config --get commit.template [12ms]
2025-06-07 17:32:07.087 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:32:07.110 [info] > git status -z -uall [8ms]
2025-06-07 17:32:07.112 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:32:12.142 [info] > git config --get commit.template [13ms]
2025-06-07 17:32:12.143 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:32:12.172 [info] > git status -z -uall [16ms]
2025-06-07 17:32:12.173 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:32:17.192 [info] > git config --get commit.template [7ms]
2025-06-07 17:32:17.193 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:32:17.204 [info] > git status -z -uall [5ms]
2025-06-07 17:32:17.206 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:32:22.226 [info] > git config --get commit.template [7ms]
2025-06-07 17:32:22.227 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:32:22.245 [info] > git status -z -uall [7ms]
2025-06-07 17:32:22.246 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:32:27.269 [info] > git config --get commit.template [7ms]
2025-06-07 17:32:27.271 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:32:27.284 [info] > git status -z -uall [7ms]
2025-06-07 17:32:27.285 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:32:32.298 [info] > git config --get commit.template [2ms]
2025-06-07 17:32:32.307 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:32:32.320 [info] > git status -z -uall [6ms]
2025-06-07 17:32:32.322 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:32:37.355 [info] > git config --get commit.template [13ms]
2025-06-07 17:32:37.357 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:32:37.383 [info] > git status -z -uall [12ms]
2025-06-07 17:32:37.385 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:32:42.402 [info] > git config --get commit.template [6ms]
2025-06-07 17:32:42.403 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:32:42.415 [info] > git status -z -uall [6ms]
2025-06-07 17:32:42.417 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:32:47.433 [info] > git config --get commit.template [2ms]
2025-06-07 17:32:47.447 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:32:47.470 [info] > git status -z -uall [12ms]
2025-06-07 17:32:47.471 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:32:52.495 [info] > git config --get commit.template [10ms]
2025-06-07 17:32:52.496 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:32:52.510 [info] > git status -z -uall [6ms]
2025-06-07 17:32:52.511 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:32:57.535 [info] > git config --get commit.template [4ms]
2025-06-07 17:32:57.551 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:32:57.576 [info] > git status -z -uall [14ms]
2025-06-07 17:32:57.578 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:33:02.609 [info] > git config --get commit.template [11ms]
2025-06-07 17:33:02.610 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:33:02.690 [info] > git status -z -uall [72ms]
2025-06-07 17:33:02.692 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [69ms]
2025-06-07 17:33:07.709 [info] > git config --get commit.template [5ms]
2025-06-07 17:33:07.710 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:33:07.722 [info] > git status -z -uall [6ms]
2025-06-07 17:33:07.723 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:33:12.745 [info] > git config --get commit.template [8ms]
2025-06-07 17:33:12.746 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:33:12.760 [info] > git status -z -uall [7ms]
2025-06-07 17:33:12.761 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:33:17.895 [info] > git config --get commit.template [4ms]
2025-06-07 17:33:17.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:33:17.930 [info] > git status -z -uall [9ms]
2025-06-07 17:33:17.932 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:33:22.950 [info] > git config --get commit.template [6ms]
2025-06-07 17:33:22.951 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:33:22.965 [info] > git status -z -uall [6ms]
2025-06-07 17:33:22.966 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:33:27.988 [info] > git config --get commit.template [5ms]
2025-06-07 17:33:28.007 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:33:28.035 [info] > git status -z -uall [15ms]
2025-06-07 17:33:28.035 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:33:33.057 [info] > git config --get commit.template [0ms]
2025-06-07 17:33:33.082 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 17:33:33.130 [info] > git status -z -uall [21ms]
2025-06-07 17:33:33.131 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:33:38.169 [info] > git config --get commit.template [18ms]
2025-06-07 17:33:38.171 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:33:38.197 [info] > git status -z -uall [13ms]
2025-06-07 17:33:38.197 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:33:43.272 [info] > git config --get commit.template [49ms]
2025-06-07 17:33:43.279 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 17:33:43.323 [info] > git status -z -uall [26ms]
2025-06-07 17:33:43.325 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:33:48.346 [info] > git config --get commit.template [8ms]
2025-06-07 17:33:48.347 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:33:48.358 [info] > git status -z -uall [5ms]
2025-06-07 17:33:48.359 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:33:53.378 [info] > git config --get commit.template [6ms]
2025-06-07 17:33:53.379 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:33:53.391 [info] > git status -z -uall [6ms]
2025-06-07 17:33:53.392 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:33:58.406 [info] > git config --get commit.template [2ms]
2025-06-07 17:33:58.421 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:33:58.440 [info] > git status -z -uall [9ms]
2025-06-07 17:33:58.442 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:34:03.459 [info] > git config --get commit.template [5ms]
2025-06-07 17:34:03.460 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:34:03.471 [info] > git status -z -uall [5ms]
2025-06-07 17:34:03.472 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:34:08.496 [info] > git config --get commit.template [9ms]
2025-06-07 17:34:08.519 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:34:08.560 [info] > git status -z -uall [20ms]
2025-06-07 17:34:08.563 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:34:13.589 [info] > git config --get commit.template [11ms]
2025-06-07 17:34:13.591 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:34:13.613 [info] > git status -z -uall [10ms]
2025-06-07 17:34:13.613 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:34:18.658 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-06-07 17:34:18.658 [info] > git config --get commit.template [28ms]
2025-06-07 17:34:18.717 [info] > git status -z -uall [23ms]
2025-06-07 17:34:18.719 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:34:23.778 [info] > git config --get commit.template [34ms]
2025-06-07 17:34:23.779 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 17:34:23.836 [info] > git status -z -uall [26ms]
2025-06-07 17:34:23.841 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 17:34:28.860 [info] > git config --get commit.template [7ms]
2025-06-07 17:34:28.861 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:34:28.875 [info] > git status -z -uall [8ms]
2025-06-07 17:34:28.878 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:34:33.896 [info] > git config --get commit.template [6ms]
2025-06-07 17:34:33.897 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:34:33.908 [info] > git status -z -uall [6ms]
2025-06-07 17:34:33.909 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:34:38.943 [info] > git config --get commit.template [18ms]
2025-06-07 17:34:38.945 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:34:38.967 [info] > git status -z -uall [12ms]
2025-06-07 17:34:38.979 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-07 17:34:43.997 [info] > git config --get commit.template [6ms]
2025-06-07 17:34:43.999 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:34:44.018 [info] > git status -z -uall [6ms]
2025-06-07 17:34:44.020 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:34:49.039 [info] > git config --get commit.template [6ms]
2025-06-07 17:34:49.041 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:34:49.056 [info] > git status -z -uall [8ms]
2025-06-07 17:34:49.057 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:34:54.074 [info] > git config --get commit.template [6ms]
2025-06-07 17:34:54.076 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:34:54.088 [info] > git status -z -uall [6ms]
2025-06-07 17:34:54.089 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:34:59.118 [info] > git config --get commit.template [2ms]
2025-06-07 17:34:59.139 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:34:59.161 [info] > git status -z -uall [9ms]
2025-06-07 17:34:59.161 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:35:04.182 [info] > git config --get commit.template [6ms]
2025-06-07 17:35:04.184 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:35:04.196 [info] > git status -z -uall [7ms]
2025-06-07 17:35:04.198 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:35:09.245 [info] > git config --get commit.template [32ms]
2025-06-07 17:35:09.247 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:35:09.276 [info] > git status -z -uall [12ms]
2025-06-07 17:35:09.277 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:35:14.321 [info] > git config --get commit.template [29ms]
2025-06-07 17:35:14.328 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 17:35:14.355 [info] > git status -z -uall [10ms]
2025-06-07 17:35:14.356 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:35:19.389 [info] > git config --get commit.template [13ms]
2025-06-07 17:35:19.406 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-06-07 17:35:19.425 [info] > git status -z -uall [7ms]
2025-06-07 17:35:19.426 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:35:24.459 [info] > git config --get commit.template [14ms]
2025-06-07 17:35:24.461 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:35:24.491 [info] > git status -z -uall [17ms]
2025-06-07 17:35:24.491 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [0ms]
2025-06-07 17:35:29.515 [info] > git config --get commit.template [10ms]
2025-06-07 17:35:29.517 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:35:29.535 [info] > git status -z -uall [9ms]
2025-06-07 17:35:29.536 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:35:34.554 [info] > git config --get commit.template [2ms]
2025-06-07 17:35:34.566 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:35:34.590 [info] > git status -z -uall [12ms]
2025-06-07 17:35:34.592 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:35:39.626 [info] > git config --get commit.template [13ms]
2025-06-07 17:35:39.628 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:35:39.665 [info] > git status -z -uall [26ms]
2025-06-07 17:35:39.668 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:35:44.714 [info] > git config --get commit.template [7ms]
2025-06-07 17:35:44.715 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:35:44.733 [info] > git status -z -uall [10ms]
2025-06-07 17:35:44.734 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 17:35:49.755 [info] > git config --get commit.template [9ms]
2025-06-07 17:35:49.756 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:35:49.821 [info] > git status -z -uall [58ms]
2025-06-07 17:35:49.822 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [53ms]
2025-06-07 17:35:54.840 [info] > git config --get commit.template [6ms]
2025-06-07 17:35:54.841 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:35:54.856 [info] > git status -z -uall [7ms]
2025-06-07 17:35:54.857 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:35:59.878 [info] > git config --get commit.template [8ms]
2025-06-07 17:35:59.880 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:35:59.894 [info] > git status -z -uall [6ms]
2025-06-07 17:35:59.894 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:36:04.910 [info] > git config --get commit.template [2ms]
2025-06-07 17:36:04.933 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-06-07 17:36:04.977 [info] > git status -z -uall [21ms]
2025-06-07 17:36:04.980 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 17:36:10.003 [info] > git config --get commit.template [7ms]
2025-06-07 17:36:10.004 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:36:10.015 [info] > git status -z -uall [6ms]
2025-06-07 17:36:10.016 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:36:15.053 [info] > git config --get commit.template [16ms]
2025-06-07 17:36:15.056 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 17:36:15.081 [info] > git status -z -uall [11ms]
2025-06-07 17:36:15.084 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:36:20.140 [info] > git config --get commit.template [41ms]
2025-06-07 17:36:20.144 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 17:36:20.183 [info] > git status -z -uall [18ms]
2025-06-07 17:36:20.185 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:36:25.223 [info] > git config --get commit.template [11ms]
2025-06-07 17:36:25.224 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:36:25.253 [info] > git status -z -uall [11ms]
2025-06-07 17:36:25.255 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:36:30.272 [info] > git config --get commit.template [0ms]
2025-06-07 17:36:30.287 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:36:30.320 [info] > git status -z -uall [16ms]
2025-06-07 17:36:30.323 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:36:35.369 [info] > git config --get commit.template [24ms]
2025-06-07 17:36:35.372 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 17:36:35.413 [info] > git status -z -uall [20ms]
2025-06-07 17:36:35.417 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 17:36:40.448 [info] > git config --get commit.template [13ms]
2025-06-07 17:36:40.450 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:36:40.478 [info] > git status -z -uall [15ms]
2025-06-07 17:36:40.479 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:36:45.496 [info] > git config --get commit.template [6ms]
2025-06-07 17:36:45.497 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:36:45.511 [info] > git status -z -uall [7ms]
2025-06-07 17:36:45.512 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:36:50.530 [info] > git config --get commit.template [7ms]
2025-06-07 17:36:50.531 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:36:50.546 [info] > git status -z -uall [7ms]
2025-06-07 17:36:50.547 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:36:55.578 [info] > git config --get commit.template [16ms]
2025-06-07 17:36:55.581 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 17:36:55.605 [info] > git status -z -uall [11ms]
2025-06-07 17:36:55.606 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:37:00.624 [info] > git config --get commit.template [6ms]
2025-06-07 17:37:00.626 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:37:00.649 [info] > git status -z -uall [9ms]
2025-06-07 17:37:00.651 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:37:05.711 [info] > git config --get commit.template [9ms]
2025-06-07 17:37:05.712 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:37:05.729 [info] > git status -z -uall [9ms]
2025-06-07 17:37:05.729 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:37:10.757 [info] > git config --get commit.template [12ms]
2025-06-07 17:37:10.758 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:37:10.791 [info] > git status -z -uall [13ms]
2025-06-07 17:37:10.792 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:37:15.807 [info] > git config --get commit.template [2ms]
2025-06-07 17:37:15.818 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 17:37:15.850 [info] > git status -z -uall [11ms]
2025-06-07 17:37:15.851 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:37:20.870 [info] > git config --get commit.template [7ms]
2025-06-07 17:37:20.872 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:37:20.885 [info] > git status -z -uall [7ms]
2025-06-07 17:37:20.887 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:37:25.904 [info] > git config --get commit.template [7ms]
2025-06-07 17:37:25.906 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:37:25.920 [info] > git status -z -uall [6ms]
2025-06-07 17:37:25.921 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:37:30.937 [info] > git config --get commit.template [3ms]
2025-06-07 17:37:30.949 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:37:30.971 [info] > git status -z -uall [11ms]
2025-06-07 17:37:30.972 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:37:35.990 [info] > git config --get commit.template [6ms]
2025-06-07 17:37:35.991 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:37:36.005 [info] > git status -z -uall [8ms]
2025-06-07 17:37:36.006 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:37:41.025 [info] > git config --get commit.template [6ms]
2025-06-07 17:37:41.026 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:37:41.042 [info] > git status -z -uall [7ms]
2025-06-07 17:37:41.043 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:37:46.068 [info] > git config --get commit.template [12ms]
2025-06-07 17:37:46.070 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:37:46.091 [info] > git status -z -uall [9ms]
2025-06-07 17:37:46.092 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:37:51.116 [info] > git config --get commit.template [9ms]
2025-06-07 17:37:51.117 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:37:51.131 [info] > git status -z -uall [8ms]
2025-06-07 17:37:51.132 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:37:56.152 [info] > git config --get commit.template [8ms]
2025-06-07 17:37:56.153 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:37:56.169 [info] > git status -z -uall [7ms]
2025-06-07 17:37:56.170 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:38:01.187 [info] > git config --get commit.template [6ms]
2025-06-07 17:38:01.188 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:38:01.199 [info] > git status -z -uall [6ms]
2025-06-07 17:38:01.200 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:38:06.219 [info] > git config --get commit.template [1ms]
2025-06-07 17:38:06.238 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:38:06.261 [info] > git status -z -uall [10ms]
2025-06-07 17:38:06.262 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:38:11.285 [info] > git config --get commit.template [7ms]
2025-06-07 17:38:11.286 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:38:11.299 [info] > git status -z -uall [7ms]
2025-06-07 17:38:11.299 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:38:16.318 [info] > git config --get commit.template [7ms]
2025-06-07 17:38:16.319 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:38:16.335 [info] > git status -z -uall [7ms]
2025-06-07 17:38:16.336 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:38:21.363 [info] > git config --get commit.template [12ms]
2025-06-07 17:38:21.365 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:38:21.385 [info] > git status -z -uall [9ms]
2025-06-07 17:38:21.387 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:38:26.406 [info] > git config --get commit.template [7ms]
2025-06-07 17:38:26.407 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:38:26.420 [info] > git status -z -uall [6ms]
2025-06-07 17:38:26.421 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:38:31.438 [info] > git config --get commit.template [5ms]
2025-06-07 17:38:31.440 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:38:31.453 [info] > git status -z -uall [6ms]
2025-06-07 17:38:31.453 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:38:36.473 [info] > git config --get commit.template [7ms]
2025-06-07 17:38:36.474 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:38:36.488 [info] > git status -z -uall [7ms]
2025-06-07 17:38:36.489 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:38:41.524 [info] > git config --get commit.template [15ms]
2025-06-07 17:38:41.526 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:38:41.562 [info] > git status -z -uall [17ms]
2025-06-07 17:38:41.563 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:38:46.595 [info] > git config --get commit.template [4ms]
2025-06-07 17:38:46.618 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-07 17:38:46.658 [info] > git status -z -uall [20ms]
2025-06-07 17:38:46.659 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:38:51.678 [info] > git config --get commit.template [6ms]
2025-06-07 17:38:51.680 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:38:51.691 [info] > git status -z -uall [6ms]
2025-06-07 17:38:51.692 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:38:56.717 [info] > git config --get commit.template [13ms]
2025-06-07 17:38:56.717 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:38:56.737 [info] > git status -z -uall [8ms]
2025-06-07 17:38:56.739 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:39:01.757 [info] > git config --get commit.template [6ms]
2025-06-07 17:39:01.759 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:39:01.774 [info] > git status -z -uall [7ms]
2025-06-07 17:39:01.778 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:39:06.811 [info] > git config --get commit.template [14ms]
2025-06-07 17:39:06.815 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 17:39:06.843 [info] > git status -z -uall [15ms]
2025-06-07 17:39:06.845 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:39:11.867 [info] > git config --get commit.template [8ms]
2025-06-07 17:39:11.869 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:39:11.884 [info] > git status -z -uall [8ms]
2025-06-07 17:39:11.885 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:39:16.903 [info] > git config --get commit.template [3ms]
2025-06-07 17:39:16.915 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:39:16.938 [info] > git status -z -uall [10ms]
2025-06-07 17:39:16.940 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:39:21.966 [info] > git config --get commit.template [7ms]
2025-06-07 17:39:21.967 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:39:21.985 [info] > git status -z -uall [9ms]
2025-06-07 17:39:21.987 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:39:27.016 [info] > git config --get commit.template [13ms]
2025-06-07 17:39:27.017 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:39:27.043 [info] > git status -z -uall [16ms]
2025-06-07 17:39:27.043 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:39:32.065 [info] > git config --get commit.template [9ms]
2025-06-07 17:39:32.067 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:39:32.081 [info] > git status -z -uall [5ms]
2025-06-07 17:39:32.083 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:39:37.101 [info] > git config --get commit.template [6ms]
2025-06-07 17:39:37.102 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:39:37.119 [info] > git status -z -uall [9ms]
2025-06-07 17:39:37.120 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:39:42.144 [info] > git config --get commit.template [9ms]
2025-06-07 17:39:42.145 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:39:42.158 [info] > git status -z -uall [6ms]
2025-06-07 17:39:42.159 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:39:47.183 [info] > git config --get commit.template [11ms]
2025-06-07 17:39:47.185 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:39:47.206 [info] > git status -z -uall [9ms]
2025-06-07 17:39:47.207 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:39:52.230 [info] > git config --get commit.template [1ms]
2025-06-07 17:39:52.255 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:39:52.302 [info] > git status -z -uall [30ms]
2025-06-07 17:39:52.306 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 17:39:57.347 [info] > git config --get commit.template [16ms]
2025-06-07 17:39:57.349 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:39:57.382 [info] > git status -z -uall [17ms]
2025-06-07 17:39:57.384 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:40:02.416 [info] > git config --get commit.template [14ms]
2025-06-07 17:40:02.429 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-06-07 17:40:02.460 [info] > git status -z -uall [16ms]
2025-06-07 17:40:02.463 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:40:07.516 [info] > git config --get commit.template [20ms]
2025-06-07 17:40:07.520 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 17:40:07.560 [info] > git status -z -uall [23ms]
2025-06-07 17:40:07.562 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:40:12.591 [info] > git config --get commit.template [13ms]
2025-06-07 17:40:12.594 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:40:12.634 [info] > git status -z -uall [22ms]
2025-06-07 17:40:12.639 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 17:40:17.727 [info] > git config --get commit.template [11ms]
2025-06-07 17:40:17.729 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:40:17.758 [info] > git status -z -uall [17ms]
2025-06-07 17:40:17.759 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-07 17:40:22.788 [info] > git config --get commit.template [10ms]
2025-06-07 17:40:22.789 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:40:22.816 [info] > git status -z -uall [15ms]
2025-06-07 17:40:22.819 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:40:27.836 [info] > git config --get commit.template [1ms]
2025-06-07 17:40:27.850 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:40:27.875 [info] > git status -z -uall [14ms]
2025-06-07 17:40:27.876 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:40:32.907 [info] > git config --get commit.template [17ms]
2025-06-07 17:40:32.908 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:40:32.927 [info] > git status -z -uall [9ms]
2025-06-07 17:40:32.929 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:40:37.948 [info] > git config --get commit.template [7ms]
2025-06-07 17:40:37.949 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:40:37.963 [info] > git status -z -uall [6ms]
2025-06-07 17:40:37.964 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:40:43.002 [info] > git config --get commit.template [17ms]
2025-06-07 17:40:43.006 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 17:40:43.028 [info] > git status -z -uall [9ms]
2025-06-07 17:40:43.030 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:40:48.058 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:40:48.058 [info] > git config --get commit.template [13ms]
2025-06-07 17:40:48.077 [info] > git status -z -uall [8ms]
2025-06-07 17:40:48.081 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:40:53.103 [info] > git config --get commit.template [9ms]
2025-06-07 17:40:53.104 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:40:53.118 [info] > git status -z -uall [8ms]
2025-06-07 17:40:53.118 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:40:58.142 [info] > git config --get commit.template [9ms]
2025-06-07 17:40:58.144 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:40:58.166 [info] > git status -z -uall [12ms]
2025-06-07 17:40:58.168 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:41:03.185 [info] > git config --get commit.template [0ms]
2025-06-07 17:41:03.198 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:41:03.239 [info] > git status -z -uall [19ms]
2025-06-07 17:41:03.240 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:41:08.290 [info] > git config --get commit.template [22ms]
2025-06-07 17:41:08.293 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 17:41:08.341 [info] > git status -z -uall [26ms]
2025-06-07 17:41:08.343 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:41:13.375 [info] > git config --get commit.template [12ms]
2025-06-07 17:41:13.377 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:41:13.404 [info] > git status -z -uall [15ms]
2025-06-07 17:41:13.405 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:41:18.442 [info] > git config --get commit.template [17ms]
2025-06-07 17:41:18.444 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:41:18.472 [info] > git status -z -uall [10ms]
2025-06-07 17:41:18.474 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:41:23.508 [info] > git config --get commit.template [14ms]
2025-06-07 17:41:23.509 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:41:23.540 [info] > git status -z -uall [15ms]
2025-06-07 17:41:23.541 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:41:28.569 [info] > git config --get commit.template [14ms]
2025-06-07 17:41:28.571 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:41:28.592 [info] > git status -z -uall [11ms]
2025-06-07 17:41:28.593 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:41:33.613 [info] > git config --get commit.template [7ms]
2025-06-07 17:41:33.614 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:41:33.627 [info] > git status -z -uall [7ms]
2025-06-07 17:41:33.628 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:41:38.645 [info] > git config --get commit.template [2ms]
2025-06-07 17:41:38.719 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [61ms]
2025-06-07 17:41:38.739 [info] > git status -z -uall [8ms]
2025-06-07 17:41:38.741 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:41:43.767 [info] > git config --get commit.template [13ms]
2025-06-07 17:41:43.768 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:41:43.792 [info] > git status -z -uall [12ms]
2025-06-07 17:41:43.797 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 17:41:48.818 [info] > git config --get commit.template [9ms]
2025-06-07 17:41:48.819 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:41:48.846 [info] > git status -z -uall [16ms]
2025-06-07 17:41:48.846 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-07 17:41:53.868 [info] > git config --get commit.template [7ms]
2025-06-07 17:41:53.869 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:41:53.881 [info] > git status -z -uall [6ms]
2025-06-07 17:41:53.883 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:41:58.901 [info] > git config --get commit.template [6ms]
2025-06-07 17:41:58.902 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:41:58.912 [info] > git status -z -uall [5ms]
2025-06-07 17:41:58.913 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:42:03.935 [info] > git config --get commit.template [8ms]
2025-06-07 17:42:03.936 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:42:03.949 [info] > git status -z -uall [6ms]
2025-06-07 17:42:03.950 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:42:08.974 [info] > git config --get commit.template [8ms]
2025-06-07 17:42:08.976 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:42:08.995 [info] > git status -z -uall [9ms]
2025-06-07 17:42:08.995 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:42:14.014 [info] > git config --get commit.template [0ms]
2025-06-07 17:42:14.035 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:42:14.063 [info] > git status -z -uall [13ms]
2025-06-07 17:42:14.065 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:42:19.082 [info] > git config --get commit.template [5ms]
2025-06-07 17:42:19.084 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:42:19.095 [info] > git status -z -uall [6ms]
2025-06-07 17:42:19.096 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:42:24.114 [info] > git config --get commit.template [7ms]
2025-06-07 17:42:24.115 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:42:24.130 [info] > git status -z -uall [7ms]
2025-06-07 17:42:24.131 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:42:29.154 [info] > git config --get commit.template [10ms]
2025-06-07 17:42:29.155 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:42:29.171 [info] > git status -z -uall [8ms]
2025-06-07 17:42:29.172 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:42:34.192 [info] > git config --get commit.template [7ms]
2025-06-07 17:42:34.193 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:42:34.208 [info] > git status -z -uall [8ms]
2025-06-07 17:42:34.210 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:42:39.230 [info] > git config --get commit.template [6ms]
2025-06-07 17:42:39.231 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:42:39.242 [info] > git status -z -uall [5ms]
2025-06-07 17:42:39.243 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:42:44.266 [info] > git config --get commit.template [10ms]
2025-06-07 17:42:44.267 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:42:44.285 [info] > git status -z -uall [9ms]
2025-06-07 17:42:44.286 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:42:49.307 [info] > git config --get commit.template [7ms]
2025-06-07 17:42:49.308 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:42:49.320 [info] > git status -z -uall [5ms]
2025-06-07 17:42:49.321 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:42:54.345 [info] > git config --get commit.template [10ms]
2025-06-07 17:42:54.346 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:42:54.362 [info] > git status -z -uall [8ms]
2025-06-07 17:42:54.363 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:42:59.391 [info] > git config --get commit.template [14ms]
2025-06-07 17:42:59.393 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 17:42:59.413 [info] > git status -z -uall [10ms]
2025-06-07 17:42:59.415 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:43:04.434 [info] > git config --get commit.template [7ms]
2025-06-07 17:43:04.435 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:43:04.448 [info] > git status -z -uall [6ms]
2025-06-07 17:43:04.449 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:43:09.476 [info] > git config --get commit.template [13ms]
2025-06-07 17:43:09.477 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:43:09.502 [info] > git status -z -uall [12ms]
2025-06-07 17:43:09.503 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:43:14.520 [info] > git config --get commit.template [6ms]
2025-06-07 17:43:14.521 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:43:14.540 [info] > git status -z -uall [10ms]
2025-06-07 17:43:14.540 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:43:19.559 [info] > git config --get commit.template [7ms]
2025-06-07 17:43:19.560 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:43:19.573 [info] > git status -z -uall [6ms]
2025-06-07 17:43:19.573 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:43:24.589 [info] > git config --get commit.template [2ms]
2025-06-07 17:43:24.599 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:43:24.615 [info] > git status -z -uall [8ms]
2025-06-07 17:43:24.617 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:43:29.644 [info] > git config --get commit.template [10ms]
2025-06-07 17:43:29.645 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:43:29.724 [info] > git status -z -uall [68ms]
2025-06-07 17:43:29.725 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [62ms]
2025-06-07 17:43:34.754 [info] > git config --get commit.template [10ms]
2025-06-07 17:43:34.755 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:43:34.774 [info] > git status -z -uall [10ms]
2025-06-07 17:43:34.774 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:43:39.797 [info] > git config --get commit.template [1ms]
2025-06-07 17:43:39.816 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:43:39.840 [info] > git status -z -uall [10ms]
2025-06-07 17:43:39.844 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:43:44.877 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 17:43:44.878 [info] > git config --get commit.template [16ms]
2025-06-07 17:43:44.905 [info] > git status -z -uall [15ms]
2025-06-07 17:43:44.907 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:43:49.962 [info] > git config --get commit.template [25ms]
2025-06-07 17:43:49.962 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:43:49.987 [info] > git status -z -uall [12ms]
2025-06-07 17:43:49.993 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-07 17:43:55.033 [info] > git config --get commit.template [16ms]
2025-06-07 17:43:55.036 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 17:43:55.067 [info] > git status -z -uall [17ms]
2025-06-07 17:43:55.067 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:44:00.101 [info] > git config --get commit.template [13ms]
2025-06-07 17:44:00.103 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:44:00.125 [info] > git status -z -uall [13ms]
2025-06-07 17:44:00.126 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:44:05.172 [info] > git config --get commit.template [21ms]
2025-06-07 17:44:05.174 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-07 17:44:05.216 [info] > git status -z -uall [24ms]
2025-06-07 17:44:05.218 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:44:10.248 [info] > git config --get commit.template [13ms]
2025-06-07 17:44:10.249 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:44:10.272 [info] > git status -z -uall [12ms]
2025-06-07 17:44:10.273 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:44:15.303 [info] > git config --get commit.template [11ms]
2025-06-07 17:44:15.305 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:44:15.330 [info] > git status -z -uall [12ms]
2025-06-07 17:44:15.332 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:44:20.372 [info] > git config --get commit.template [14ms]
2025-06-07 17:44:20.373 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:44:20.394 [info] > git status -z -uall [11ms]
2025-06-07 17:44:20.394 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:44:25.420 [info] > git config --get commit.template [9ms]
2025-06-07 17:44:25.422 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:44:25.443 [info] > git status -z -uall [10ms]
2025-06-07 17:44:25.444 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:44:30.501 [info] > git config --get commit.template [24ms]
2025-06-07 17:44:30.508 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-07 17:44:30.559 [info] > git status -z -uall [20ms]
2025-06-07 17:44:30.566 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-07 17:44:35.623 [info] > git config --get commit.template [15ms]
2025-06-07 17:44:35.626 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 17:44:35.649 [info] > git status -z -uall [10ms]
2025-06-07 17:44:35.650 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:44:40.705 [info] > git config --get commit.template [21ms]
2025-06-07 17:44:40.706 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:44:40.732 [info] > git status -z -uall [11ms]
2025-06-07 17:44:40.734 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:44:45.759 [info] > git config --get commit.template [8ms]
2025-06-07 17:44:45.760 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:44:45.774 [info] > git status -z -uall [7ms]
2025-06-07 17:44:45.777 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:44:50.803 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:44:50.803 [info] > git config --get commit.template [11ms]
2025-06-07 17:44:50.821 [info] > git status -z -uall [8ms]
2025-06-07 17:44:50.823 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:44:55.854 [info] > git config --get commit.template [13ms]
2025-06-07 17:44:55.856 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:44:55.876 [info] > git status -z -uall [8ms]
2025-06-07 17:44:55.877 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:45:00.895 [info] > git config --get commit.template [6ms]
2025-06-07 17:45:00.896 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:45:00.909 [info] > git status -z -uall [6ms]
2025-06-07 17:45:00.910 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:45:05.926 [info] > git config --get commit.template [1ms]
2025-06-07 17:45:05.939 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:45:05.966 [info] > git status -z -uall [12ms]
2025-06-07 17:45:05.967 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:45:11.001 [info] > git config --get commit.template [17ms]
2025-06-07 17:45:11.003 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:45:11.032 [info] > git status -z -uall [13ms]
2025-06-07 17:45:11.034 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:45:16.068 [info] > git config --get commit.template [13ms]
2025-06-07 17:45:16.070 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:45:16.085 [info] > git status -z -uall [7ms]
2025-06-07 17:45:16.086 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:45:21.108 [info] > git config --get commit.template [9ms]
2025-06-07 17:45:21.110 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:45:21.127 [info] > git status -z -uall [8ms]
2025-06-07 17:45:21.128 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:45:26.178 [info] > git config --get commit.template [26ms]
2025-06-07 17:45:26.181 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:45:26.223 [info] > git status -z -uall [20ms]
2025-06-07 17:45:26.226 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:45:31.243 [info] > git config --get commit.template [2ms]
2025-06-07 17:45:31.256 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:45:31.272 [info] > git status -z -uall [8ms]
2025-06-07 17:45:31.272 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:45:36.290 [info] > git config --get commit.template [3ms]
2025-06-07 17:45:36.315 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:45:36.347 [info] > git status -z -uall [12ms]
2025-06-07 17:45:36.348 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:45:41.411 [info] > git config --get commit.template [30ms]
2025-06-07 17:45:41.414 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:45:41.457 [info] > git status -z -uall [19ms]
2025-06-07 17:45:41.460 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:45:46.496 [info] > git config --get commit.template [19ms]
2025-06-07 17:45:46.498 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:45:46.543 [info] > git status -z -uall [30ms]
2025-06-07 17:45:46.545 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:45:51.574 [info] > git config --get commit.template [4ms]
2025-06-07 17:45:51.597 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 17:45:51.663 [info] > git status -z -uall [30ms]
2025-06-07 17:45:51.676 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [14ms]
2025-06-07 17:45:56.756 [info] > git config --get commit.template [13ms]
2025-06-07 17:45:56.757 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:45:56.778 [info] > git status -z -uall [10ms]
2025-06-07 17:45:56.778 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:46:01.814 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:46:01.814 [info] > git config --get commit.template [17ms]
2025-06-07 17:46:01.844 [info] > git status -z -uall [15ms]
2025-06-07 17:46:01.850 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 17:46:06.879 [info] > git config --get commit.template [11ms]
2025-06-07 17:46:06.881 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:46:06.919 [info] > git status -z -uall [25ms]
2025-06-07 17:46:06.920 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:46:11.958 [info] > git config --get commit.template [19ms]
2025-06-07 17:46:11.964 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 17:46:11.997 [info] > git status -z -uall [16ms]
2025-06-07 17:46:11.998 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:46:17.022 [info] > git config --get commit.template [7ms]
2025-06-07 17:46:17.024 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:46:17.035 [info] > git status -z -uall [5ms]
2025-06-07 17:46:17.037 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:46:22.057 [info] > git config --get commit.template [7ms]
2025-06-07 17:46:22.058 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:46:22.071 [info] > git status -z -uall [7ms]
2025-06-07 17:46:22.072 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:46:27.090 [info] > git config --get commit.template [6ms]
2025-06-07 17:46:27.091 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:46:27.102 [info] > git status -z -uall [5ms]
2025-06-07 17:46:27.104 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:46:32.124 [info] > git config --get commit.template [2ms]
2025-06-07 17:46:32.136 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:46:32.161 [info] > git status -z -uall [12ms]
2025-06-07 17:46:32.162 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:46:37.183 [info] > git config --get commit.template [2ms]
2025-06-07 17:46:37.199 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:46:37.239 [info] > git status -z -uall [20ms]
2025-06-07 17:46:37.242 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:46:42.261 [info] > git config --get commit.template [6ms]
2025-06-07 17:46:42.262 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:46:42.273 [info] > git status -z -uall [6ms]
2025-06-07 17:46:42.274 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:46:47.301 [info] > git config --get commit.template [12ms]
2025-06-07 17:46:47.303 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:46:47.339 [info] > git status -z -uall [20ms]
2025-06-07 17:46:47.340 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:46:52.361 [info] > git config --get commit.template [7ms]
2025-06-07 17:46:52.362 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:46:52.373 [info] > git status -z -uall [5ms]
2025-06-07 17:46:52.374 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:46:57.395 [info] > git config --get commit.template [1ms]
2025-06-07 17:46:57.414 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 17:46:57.457 [info] > git status -z -uall [22ms]
2025-06-07 17:46:57.459 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:47:02.483 [info] > git config --get commit.template [0ms]
2025-06-07 17:47:02.505 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:47:02.544 [info] > git status -z -uall [17ms]
2025-06-07 17:47:02.544 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:47:07.587 [info] > git config --get commit.template [21ms]
2025-06-07 17:47:07.589 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:47:07.636 [info] > git status -z -uall [23ms]
2025-06-07 17:47:07.640 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:47:12.678 [info] > git config --get commit.template [16ms]
2025-06-07 17:47:12.680 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:47:12.720 [info] > git status -z -uall [19ms]
2025-06-07 17:47:12.721 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:47:17.897 [info] > git config --get commit.template [37ms]
2025-06-07 17:47:17.902 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 17:47:17.992 [info] > git status -z -uall [41ms]
2025-06-07 17:47:17.996 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-07 17:47:23.025 [info] > git config --get commit.template [11ms]
2025-06-07 17:47:23.027 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:47:23.050 [info] > git status -z -uall [12ms]
2025-06-07 17:47:23.051 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:47:28.072 [info] > git config --get commit.template [8ms]
2025-06-07 17:47:28.074 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:47:28.086 [info] > git status -z -uall [5ms]
2025-06-07 17:47:28.087 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:47:33.106 [info] > git config --get commit.template [7ms]
2025-06-07 17:47:33.107 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:47:33.119 [info] > git status -z -uall [5ms]
2025-06-07 17:47:33.120 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:47:38.157 [info] > git config --get commit.template [18ms]
2025-06-07 17:47:38.161 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 17:47:38.186 [info] > git status -z -uall [12ms]
2025-06-07 17:47:38.187 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:47:43.208 [info] > git config --get commit.template [9ms]
2025-06-07 17:47:43.212 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 17:47:43.237 [info] > git status -z -uall [9ms]
2025-06-07 17:47:43.238 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:47:48.267 [info] > git config --get commit.template [13ms]
2025-06-07 17:47:48.268 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:47:48.295 [info] > git status -z -uall [13ms]
2025-06-07 17:47:48.296 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:47:53.321 [info] > git config --get commit.template [12ms]
2025-06-07 17:47:53.322 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:47:53.363 [info] > git status -z -uall [29ms]
2025-06-07 17:47:53.366 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:47:58.396 [info] > git config --get commit.template [10ms]
2025-06-07 17:47:58.397 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:47:58.431 [info] > git status -z -uall [12ms]
2025-06-07 17:47:58.432 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:48:03.450 [info] > git config --get commit.template [5ms]
2025-06-07 17:48:03.452 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:48:03.462 [info] > git status -z -uall [5ms]
2025-06-07 17:48:03.463 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:48:08.483 [info] > git config --get commit.template [6ms]
2025-06-07 17:48:08.485 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:48:08.498 [info] > git status -z -uall [7ms]
2025-06-07 17:48:08.500 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:48:13.535 [info] > git config --get commit.template [16ms]
2025-06-07 17:48:13.538 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:48:13.563 [info] > git status -z -uall [10ms]
2025-06-07 17:48:13.565 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:48:18.596 [info] > git config --get commit.template [11ms]
2025-06-07 17:48:18.597 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:48:18.612 [info] > git status -z -uall [7ms]
2025-06-07 17:48:18.613 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:48:23.639 [info] > git config --get commit.template [12ms]
2025-06-07 17:48:23.640 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:48:23.660 [info] > git status -z -uall [8ms]
2025-06-07 17:48:23.662 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:48:28.687 [info] > git config --get commit.template [11ms]
2025-06-07 17:48:28.688 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:48:28.705 [info] > git status -z -uall [9ms]
2025-06-07 17:48:28.706 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:48:33.723 [info] > git config --get commit.template [5ms]
2025-06-07 17:48:33.724 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:48:33.735 [info] > git status -z -uall [5ms]
2025-06-07 17:48:33.736 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:48:38.771 [info] > git config --get commit.template [8ms]
2025-06-07 17:48:38.772 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:48:38.788 [info] > git status -z -uall [8ms]
2025-06-07 17:48:38.789 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:48:43.836 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 17:48:43.836 [info] > git config --get commit.template [28ms]
2025-06-07 17:48:43.864 [info] > git status -z -uall [15ms]
2025-06-07 17:48:43.865 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:48:48.883 [info] > git config --get commit.template [1ms]
2025-06-07 17:48:48.905 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:48:48.930 [info] > git status -z -uall [12ms]
2025-06-07 17:48:48.931 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:48:53.950 [info] > git config --get commit.template [6ms]
2025-06-07 17:48:53.952 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:48:53.965 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:48:53.967 [info] > git status -z -uall [9ms]
2025-06-07 17:48:58.985 [info] > git config --get commit.template [5ms]
2025-06-07 17:48:58.986 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:48:59.000 [info] > git status -z -uall [7ms]
2025-06-07 17:48:59.002 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:49:04.026 [info] > git config --get commit.template [9ms]
2025-06-07 17:49:04.027 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:49:04.040 [info] > git status -z -uall [7ms]
2025-06-07 17:49:04.041 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:49:09.063 [info] > git config --get commit.template [9ms]
2025-06-07 17:49:09.064 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:49:09.076 [info] > git status -z -uall [6ms]
2025-06-07 17:49:09.077 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:49:14.097 [info] > git config --get commit.template [7ms]
2025-06-07 17:49:14.098 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:49:14.114 [info] > git status -z -uall [7ms]
2025-06-07 17:49:14.115 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:49:19.141 [info] > git config --get commit.template [11ms]
2025-06-07 17:49:19.142 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:49:19.159 [info] > git status -z -uall [7ms]
2025-06-07 17:49:19.160 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:49:24.181 [info] > git config --get commit.template [5ms]
2025-06-07 17:49:24.200 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 17:49:24.231 [info] > git status -z -uall [15ms]
2025-06-07 17:49:24.231 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:49:29.252 [info] > git config --get commit.template [8ms]
2025-06-07 17:49:29.253 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:49:29.266 [info] > git status -z -uall [6ms]
2025-06-07 17:49:29.267 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:49:34.308 [info] > git config --get commit.template [22ms]
2025-06-07 17:49:34.309 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:49:34.329 [info] > git status -z -uall [9ms]
2025-06-07 17:49:34.330 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:49:39.350 [info] > git config --get commit.template [8ms]
2025-06-07 17:49:39.351 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:49:39.363 [info] > git status -z -uall [5ms]
2025-06-07 17:49:39.364 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:49:44.387 [info] > git config --get commit.template [8ms]
2025-06-07 17:49:44.388 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:49:44.422 [info] > git status -z -uall [21ms]
2025-06-07 17:49:44.424 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:49:49.443 [info] > git config --get commit.template [6ms]
2025-06-07 17:49:49.444 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:49:49.458 [info] > git status -z -uall [6ms]
2025-06-07 17:49:49.459 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:49:54.483 [info] > git config --get commit.template [10ms]
2025-06-07 17:49:54.485 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:49:54.500 [info] > git status -z -uall [8ms]
2025-06-07 17:49:54.502 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:49:59.535 [info] > git config --get commit.template [16ms]
2025-06-07 17:49:59.537 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:49:59.570 [info] > git status -z -uall [13ms]
2025-06-07 17:49:59.573 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:50:04.611 [info] > git config --get commit.template [18ms]
2025-06-07 17:50:04.638 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:50:04.685 [info] > git status -z -uall [26ms]
2025-06-07 17:50:04.686 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:50:09.712 [info] > git config --get commit.template [12ms]
2025-06-07 17:50:09.713 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:50:09.751 [info] > git status -z -uall [10ms]
2025-06-07 17:50:09.753 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:50:14.792 [info] > git config --get commit.template [13ms]
2025-06-07 17:50:14.794 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:50:14.826 [info] > git status -z -uall [18ms]
2025-06-07 17:50:14.828 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:50:19.856 [info] > git config --get commit.template [13ms]
2025-06-07 17:50:19.857 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:50:19.935 [info] > git status -z -uall [67ms]
2025-06-07 17:50:19.935 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [53ms]
2025-06-07 17:50:24.953 [info] > git config --get commit.template [6ms]
2025-06-07 17:50:24.955 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:50:24.967 [info] > git status -z -uall [7ms]
2025-06-07 17:50:24.968 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:50:29.993 [info] > git config --get commit.template [11ms]
2025-06-07 17:50:29.995 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:50:30.029 [info] > git status -z -uall [18ms]
2025-06-07 17:50:30.032 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:50:35.050 [info] > git config --get commit.template [7ms]
2025-06-07 17:50:35.052 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:50:35.063 [info] > git status -z -uall [5ms]
2025-06-07 17:50:35.064 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:50:40.086 [info] > git config --get commit.template [7ms]
2025-06-07 17:50:40.087 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:50:40.101 [info] > git status -z -uall [7ms]
2025-06-07 17:50:40.102 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:50:45.145 [info] > git config --get commit.template [20ms]
2025-06-07 17:50:45.153 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-07 17:50:45.194 [info] > git status -z -uall [17ms]
2025-06-07 17:50:45.197 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:50:50.221 [info] > git config --get commit.template [9ms]
2025-06-07 17:50:50.222 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:50:50.234 [info] > git status -z -uall [5ms]
2025-06-07 17:50:50.235 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:50:55.261 [info] > git config --get commit.template [9ms]
2025-06-07 17:50:55.262 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:50:55.281 [info] > git status -z -uall [9ms]
2025-06-07 17:50:55.283 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:51:00.303 [info] > git config --get commit.template [9ms]
2025-06-07 17:51:00.304 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:51:00.322 [info] > git status -z -uall [8ms]
2025-06-07 17:51:00.323 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:51:05.345 [info] > git config --get commit.template [8ms]
2025-06-07 17:51:05.346 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:51:05.364 [info] > git status -z -uall [8ms]
2025-06-07 17:51:05.366 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:51:10.399 [info] > git config --get commit.template [14ms]
2025-06-07 17:51:10.400 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:51:10.417 [info] > git status -z -uall [7ms]
2025-06-07 17:51:10.419 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:51:15.447 [info] > git config --get commit.template [14ms]
2025-06-07 17:51:15.449 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:51:15.467 [info] > git status -z -uall [9ms]
2025-06-07 17:51:15.468 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:51:20.498 [info] > git config --get commit.template [11ms]
2025-06-07 17:51:20.499 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:51:20.519 [info] > git status -z -uall [7ms]
2025-06-07 17:51:20.520 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:51:25.536 [info] > git config --get commit.template [5ms]
2025-06-07 17:51:25.537 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:51:25.550 [info] > git status -z -uall [5ms]
2025-06-07 17:51:25.552 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:51:30.574 [info] > git config --get commit.template [8ms]
2025-06-07 17:51:30.576 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:51:30.588 [info] > git status -z -uall [6ms]
2025-06-07 17:51:30.589 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:51:35.609 [info] > git config --get commit.template [7ms]
2025-06-07 17:51:35.611 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:51:35.623 [info] > git status -z -uall [6ms]
2025-06-07 17:51:35.625 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:51:40.642 [info] > git config --get commit.template [6ms]
2025-06-07 17:51:40.643 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:51:40.655 [info] > git status -z -uall [5ms]
2025-06-07 17:51:40.656 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:51:45.678 [info] > git config --get commit.template [8ms]
2025-06-07 17:51:45.679 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:51:45.693 [info] > git status -z -uall [6ms]
2025-06-07 17:51:45.696 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:51:50.804 [info] > git config --get commit.template [29ms]
2025-06-07 17:51:51.107 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [304ms]
2025-06-07 17:51:51.122 [info] > git status -z -uall [5ms]
2025-06-07 17:51:51.124 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:51:56.150 [info] > git config --get commit.template [10ms]
2025-06-07 17:51:56.152 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:51:56.169 [info] > git status -z -uall [8ms]
2025-06-07 17:51:56.170 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:52:01.195 [info] > git config --get commit.template [9ms]
2025-06-07 17:52:01.196 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:52:01.209 [info] > git status -z -uall [6ms]
2025-06-07 17:52:01.210 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:52:06.297 [info] > git config --get commit.template [8ms]
2025-06-07 17:52:06.298 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:52:06.309 [info] > git status -z -uall [5ms]
2025-06-07 17:52:06.310 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:52:11.331 [info] > git config --get commit.template [8ms]
2025-06-07 17:52:11.332 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:52:11.343 [info] > git status -z -uall [6ms]
2025-06-07 17:52:11.344 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:52:16.366 [info] > git config --get commit.template [8ms]
2025-06-07 17:52:16.367 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:52:16.379 [info] > git status -z -uall [5ms]
2025-06-07 17:52:16.380 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:52:21.400 [info] > git config --get commit.template [8ms]
2025-06-07 17:52:21.401 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:52:21.414 [info] > git status -z -uall [6ms]
2025-06-07 17:52:21.415 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:52:26.444 [info] > git config --get commit.template [12ms]
2025-06-07 17:52:26.449 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-07 17:52:26.475 [info] > git status -z -uall [12ms]
2025-06-07 17:52:26.478 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:52:31.513 [info] > git config --get commit.template [15ms]
2025-06-07 17:52:31.515 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:52:31.539 [info] > git status -z -uall [8ms]
2025-06-07 17:52:31.541 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:52:36.562 [info] > git config --get commit.template [0ms]
2025-06-07 17:52:36.581 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 17:52:36.612 [info] > git status -z -uall [13ms]
2025-06-07 17:52:36.615 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:52:41.644 [info] > git config --get commit.template [12ms]
2025-06-07 17:52:41.646 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:52:41.674 [info] > git status -z -uall [11ms]
2025-06-07 17:52:41.675 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:52:46.704 [info] > git config --get commit.template [13ms]
2025-06-07 17:52:46.706 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:52:46.730 [info] > git status -z -uall [14ms]
2025-06-07 17:52:46.731 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:52:51.767 [info] > git config --get commit.template [2ms]
2025-06-07 17:52:51.787 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:52:51.824 [info] > git status -z -uall [18ms]
2025-06-07 17:52:51.825 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:52:56.942 [info] > git config --get commit.template [30ms]
2025-06-07 17:52:56.945 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-07 17:52:57.012 [info] > git status -z -uall [42ms]
2025-06-07 17:52:57.013 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:53:02.046 [info] > git config --get commit.template [14ms]
2025-06-07 17:53:02.049 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 17:53:02.071 [info] > git status -z -uall [11ms]
2025-06-07 17:53:02.072 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:53:07.098 [info] > git config --get commit.template [10ms]
2025-06-07 17:53:07.101 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 17:53:07.126 [info] > git status -z -uall [12ms]
2025-06-07 17:53:07.127 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:53:12.154 [info] > git config --get commit.template [12ms]
2025-06-07 17:53:12.156 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:53:12.181 [info] > git status -z -uall [11ms]
2025-06-07 17:53:12.182 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:53:17.206 [info] > git config --get commit.template [8ms]
2025-06-07 17:53:17.207 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:53:17.230 [info] > git status -z -uall [11ms]
2025-06-07 17:53:17.231 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:53:22.254 [info] > git config --get commit.template [8ms]
2025-06-07 17:53:22.258 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 17:53:22.274 [info] > git status -z -uall [6ms]
2025-06-07 17:53:22.275 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:53:27.296 [info] > git config --get commit.template [5ms]
2025-06-07 17:53:27.297 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:53:27.309 [info] > git status -z -uall [6ms]
2025-06-07 17:53:27.310 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:53:32.350 [info] > git config --get commit.template [22ms]
2025-06-07 17:53:32.358 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-07 17:53:32.375 [info] > git status -z -uall [7ms]
2025-06-07 17:53:32.377 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:53:37.425 [info] > git config --get commit.template [25ms]
2025-06-07 17:53:37.428 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:53:37.471 [info] > git status -z -uall [18ms]
2025-06-07 17:53:37.472 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:53:42.494 [info] > git config --get commit.template [8ms]
2025-06-07 17:53:42.496 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:53:42.512 [info] > git status -z -uall [7ms]
2025-06-07 17:53:42.513 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:53:47.531 [info] > git config --get commit.template [7ms]
2025-06-07 17:53:47.533 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:53:47.549 [info] > git status -z -uall [6ms]
2025-06-07 17:53:47.551 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:53:52.581 [info] > git config --get commit.template [14ms]
2025-06-07 17:53:52.583 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:53:52.600 [info] > git status -z -uall [7ms]
2025-06-07 17:53:52.601 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:53:57.638 [info] > git config --get commit.template [22ms]
2025-06-07 17:53:57.640 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:53:57.680 [info] > git status -z -uall [16ms]
2025-06-07 17:53:57.682 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:54:02.703 [info] > git config --get commit.template [1ms]
2025-06-07 17:54:02.719 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:54:02.844 [info] > git status -z -uall [107ms]
2025-06-07 17:54:02.844 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [78ms]
2025-06-07 17:54:07.864 [info] > git config --get commit.template [7ms]
2025-06-07 17:54:07.865 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:54:07.878 [info] > git status -z -uall [7ms]
2025-06-07 17:54:07.881 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:54:12.910 [info] > git config --get commit.template [14ms]
2025-06-07 17:54:12.911 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:54:12.939 [info] > git status -z -uall [10ms]
2025-06-07 17:54:12.940 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:54:17.967 [info] > git config --get commit.template [10ms]
2025-06-07 17:54:17.969 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:54:17.988 [info] > git status -z -uall [9ms]
2025-06-07 17:54:17.989 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:54:23.015 [info] > git config --get commit.template [10ms]
2025-06-07 17:54:23.016 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:54:23.030 [info] > git status -z -uall [6ms]
2025-06-07 17:54:23.032 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:54:28.050 [info] > git config --get commit.template [0ms]
2025-06-07 17:54:28.068 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:54:28.098 [info] > git status -z -uall [12ms]
2025-06-07 17:54:28.100 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:54:33.132 [info] > git config --get commit.template [14ms]
2025-06-07 17:54:33.135 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:54:33.157 [info] > git status -z -uall [11ms]
2025-06-07 17:54:33.158 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:54:38.185 [info] > git config --get commit.template [10ms]
2025-06-07 17:54:38.186 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:54:38.202 [info] > git status -z -uall [9ms]
2025-06-07 17:54:38.202 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:54:43.236 [info] > git config --get commit.template [16ms]
2025-06-07 17:54:43.238 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:54:43.269 [info] > git status -z -uall [15ms]
2025-06-07 17:54:43.271 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:54:48.294 [info] > git config --get commit.template [10ms]
2025-06-07 17:54:48.296 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:54:48.322 [info] > git status -z -uall [10ms]
2025-06-07 17:54:48.326 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:54:53.346 [info] > git config --get commit.template [2ms]
2025-06-07 17:54:53.361 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:54:53.379 [info] > git status -z -uall [8ms]
2025-06-07 17:54:53.380 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:54:58.403 [info] > git config --get commit.template [9ms]
2025-06-07 17:54:58.405 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:54:58.425 [info] > git status -z -uall [6ms]
2025-06-07 17:54:58.426 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:55:03.442 [info] > git config --get commit.template [6ms]
2025-06-07 17:55:03.443 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:55:03.454 [info] > git status -z -uall [6ms]
2025-06-07 17:55:03.455 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:55:08.472 [info] > git config --get commit.template [5ms]
2025-06-07 17:55:08.474 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:55:08.486 [info] > git status -z -uall [7ms]
2025-06-07 17:55:08.487 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:55:13.506 [info] > git config --get commit.template [7ms]
2025-06-07 17:55:13.507 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:55:13.520 [info] > git status -z -uall [6ms]
2025-06-07 17:55:13.521 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:55:18.538 [info] > git config --get commit.template [5ms]
2025-06-07 17:55:18.539 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:55:18.550 [info] > git status -z -uall [5ms]
2025-06-07 17:55:18.551 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:55:23.576 [info] > git config --get commit.template [9ms]
2025-06-07 17:55:23.578 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:55:23.591 [info] > git status -z -uall [6ms]
2025-06-07 17:55:23.593 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:55:28.613 [info] > git config --get commit.template [6ms]
2025-06-07 17:55:28.614 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:55:28.628 [info] > git status -z -uall [6ms]
2025-06-07 17:55:28.630 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:55:33.654 [info] > git config --get commit.template [1ms]
2025-06-07 17:55:33.671 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:55:33.708 [info] > git status -z -uall [20ms]
2025-06-07 17:55:33.711 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:55:38.788 [info] > git config --get commit.template [61ms]
2025-06-07 17:55:38.789 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:55:38.803 [info] > git status -z -uall [8ms]
2025-06-07 17:55:38.803 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:55:43.830 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:55:43.833 [info] > git config --get commit.template [17ms]
2025-06-07 17:55:43.854 [info] > git status -z -uall [10ms]
2025-06-07 17:55:43.855 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:55:48.878 [info] > git config --get commit.template [9ms]
2025-06-07 17:55:48.879 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:55:48.896 [info] > git status -z -uall [8ms]
2025-06-07 17:55:48.898 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:55:53.915 [info] > git config --get commit.template [2ms]
2025-06-07 17:55:53.933 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:55:53.956 [info] > git status -z -uall [12ms]
2025-06-07 17:55:53.956 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:55:58.972 [info] > git config --get commit.template [6ms]
2025-06-07 17:55:58.973 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:55:58.984 [info] > git status -z -uall [5ms]
2025-06-07 17:55:58.985 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:56:04.028 [info] > git config --get commit.template [24ms]
2025-06-07 17:56:04.030 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:56:04.065 [info] > git status -z -uall [16ms]
2025-06-07 17:56:04.068 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:56:09.105 [info] > git config --get commit.template [12ms]
2025-06-07 17:56:09.107 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:56:09.145 [info] > git status -z -uall [26ms]
2025-06-07 17:56:09.146 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:56:14.168 [info] > git config --get commit.template [9ms]
2025-06-07 17:56:14.170 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:56:14.184 [info] > git status -z -uall [7ms]
2025-06-07 17:56:14.185 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:56:19.213 [info] > git config --get commit.template [14ms]
2025-06-07 17:56:19.215 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:56:19.240 [info] > git status -z -uall [14ms]
2025-06-07 17:56:19.241 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:56:24.258 [info] > git config --get commit.template [6ms]
2025-06-07 17:56:24.259 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:56:24.275 [info] > git status -z -uall [10ms]
2025-06-07 17:56:24.276 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:56:29.292 [info] > git config --get commit.template [5ms]
2025-06-07 17:56:29.294 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:56:29.306 [info] > git status -z -uall [5ms]
2025-06-07 17:56:29.308 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:56:34.325 [info] > git config --get commit.template [6ms]
2025-06-07 17:56:34.327 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:56:34.340 [info] > git status -z -uall [6ms]
2025-06-07 17:56:34.341 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:56:39.362 [info] > git config --get commit.template [8ms]
2025-06-07 17:56:39.363 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:56:39.375 [info] > git status -z -uall [5ms]
2025-06-07 17:56:39.376 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:56:44.393 [info] > git config --get commit.template [2ms]
2025-06-07 17:56:44.407 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:56:44.429 [info] > git status -z -uall [10ms]
2025-06-07 17:56:44.429 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [0ms]
2025-06-07 17:56:49.448 [info] > git config --get commit.template [6ms]
2025-06-07 17:56:49.449 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:56:49.460 [info] > git status -z -uall [5ms]
2025-06-07 17:56:49.461 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:56:54.480 [info] > git config --get commit.template [7ms]
2025-06-07 17:56:54.481 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:56:54.492 [info] > git status -z -uall [5ms]
2025-06-07 17:56:54.493 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:56:59.515 [info] > git config --get commit.template [8ms]
2025-06-07 17:56:59.516 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:56:59.534 [info] > git status -z -uall [9ms]
2025-06-07 17:56:59.535 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:57:04.558 [info] > git config --get commit.template [9ms]
2025-06-07 17:57:04.560 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:57:04.577 [info] > git status -z -uall [8ms]
2025-06-07 17:57:04.578 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:57:09.598 [info] > git config --get commit.template [7ms]
2025-06-07 17:57:09.599 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:57:09.614 [info] > git status -z -uall [8ms]
2025-06-07 17:57:09.615 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:57:14.634 [info] > git config --get commit.template [8ms]
2025-06-07 17:57:14.635 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:57:14.647 [info] > git status -z -uall [5ms]
2025-06-07 17:57:14.648 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:57:19.664 [info] > git config --get commit.template [6ms]
2025-06-07 17:57:19.665 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:57:19.675 [info] > git status -z -uall [4ms]
2025-06-07 17:57:19.677 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:57:24.709 [info] > git config --get commit.template [15ms]
2025-06-07 17:57:24.712 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 17:57:24.747 [info] > git status -z -uall [17ms]
2025-06-07 17:57:24.750 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:57:29.829 [info] > git config --get commit.template [4ms]
2025-06-07 17:57:29.845 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 17:57:29.875 [info] > git status -z -uall [19ms]
2025-06-07 17:57:29.877 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:57:34.912 [info] > git config --get commit.template [15ms]
2025-06-07 17:57:34.914 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:57:34.945 [info] > git status -z -uall [16ms]
2025-06-07 17:57:34.947 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:57:40.005 [info] > git config --get commit.template [14ms]
2025-06-07 17:57:40.006 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:57:40.030 [info] > git status -z -uall [15ms]
2025-06-07 17:57:40.031 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-07 17:57:45.070 [info] > git config --get commit.template [20ms]
2025-06-07 17:57:45.070 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 17:57:45.086 [info] > git status -z -uall [7ms]
2025-06-07 17:57:45.087 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:57:50.104 [info] > git config --get commit.template [5ms]
2025-06-07 17:57:50.105 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:57:50.118 [info] > git status -z -uall [6ms]
2025-06-07 17:57:50.119 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:57:55.141 [info] > git config --get commit.template [9ms]
2025-06-07 17:57:55.142 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:57:55.155 [info] > git status -z -uall [6ms]
2025-06-07 17:57:55.156 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:58:00.180 [info] > git config --get commit.template [8ms]
2025-06-07 17:58:00.181 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:58:00.194 [info] > git status -z -uall [7ms]
2025-06-07 17:58:00.195 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:58:05.217 [info] > git config --get commit.template [10ms]
2025-06-07 17:58:05.219 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:58:05.235 [info] > git status -z -uall [6ms]
2025-06-07 17:58:05.236 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:58:10.260 [info] > git config --get commit.template [8ms]
2025-06-07 17:58:10.261 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:58:10.276 [info] > git status -z -uall [7ms]
2025-06-07 17:58:10.278 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:58:15.301 [info] > git config --get commit.template [1ms]
2025-06-07 17:58:15.318 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:58:15.351 [info] > git status -z -uall [18ms]
2025-06-07 17:58:15.352 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:58:20.385 [info] > git config --get commit.template [8ms]
2025-06-07 17:58:20.391 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-07 17:58:20.411 [info] > git status -z -uall [10ms]
2025-06-07 17:58:20.411 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:58:25.434 [info] > git config --get commit.template [1ms]
2025-06-07 17:58:25.447 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:58:25.473 [info] > git status -z -uall [15ms]
2025-06-07 17:58:25.473 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:58:30.491 [info] > git config --get commit.template [6ms]
2025-06-07 17:58:30.492 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:58:30.503 [info] > git status -z -uall [5ms]
2025-06-07 17:58:30.505 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:58:35.536 [info] > git config --get commit.template [15ms]
2025-06-07 17:58:35.538 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:58:35.555 [info] > git status -z -uall [9ms]
2025-06-07 17:58:35.557 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:58:40.578 [info] > git config --get commit.template [0ms]
2025-06-07 17:58:40.592 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:58:40.610 [info] > git status -z -uall [7ms]
2025-06-07 17:58:40.612 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:58:45.633 [info] > git config --get commit.template [7ms]
2025-06-07 17:58:45.634 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:58:45.657 [info] > git status -z -uall [11ms]
2025-06-07 17:58:45.658 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:58:50.675 [info] > git config --get commit.template [7ms]
2025-06-07 17:58:50.676 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:58:50.687 [info] > git status -z -uall [5ms]
2025-06-07 17:58:50.688 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:58:55.703 [info] > git config --get commit.template [6ms]
2025-06-07 17:58:55.706 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 17:58:55.723 [info] > git status -z -uall [7ms]
2025-06-07 17:58:55.724 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:59:00.747 [info] > git config --get commit.template [8ms]
2025-06-07 17:59:00.748 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:59:00.763 [info] > git status -z -uall [6ms]
2025-06-07 17:59:00.764 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:59:05.822 [info] > git config --get commit.template [5ms]
2025-06-07 17:59:05.834 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:59:05.851 [info] > git status -z -uall [7ms]
2025-06-07 17:59:05.852 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:59:10.910 [info] > git config --get commit.template [40ms]
2025-06-07 17:59:10.912 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:59:10.939 [info] > git status -z -uall [11ms]
2025-06-07 17:59:10.940 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:59:15.958 [info] > git config --get commit.template [5ms]
2025-06-07 17:59:15.959 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:59:15.977 [info] > git status -z -uall [9ms]
2025-06-07 17:59:15.979 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:59:21.002 [info] > git config --get commit.template [9ms]
2025-06-07 17:59:21.003 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:59:21.016 [info] > git status -z -uall [6ms]
2025-06-07 17:59:21.017 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:59:26.047 [info] > git config --get commit.template [13ms]
2025-06-07 17:59:26.048 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:59:26.068 [info] > git status -z -uall [10ms]
2025-06-07 17:59:26.069 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:59:31.101 [info] > git config --get commit.template [12ms]
2025-06-07 17:59:31.103 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:59:31.123 [info] > git status -z -uall [11ms]
2025-06-07 17:59:31.124 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:59:36.159 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 17:59:36.159 [info] > git config --get commit.template [17ms]
2025-06-07 17:59:36.183 [info] > git status -z -uall [10ms]
2025-06-07 17:59:36.185 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:59:41.203 [info] > git config --get commit.template [7ms]
2025-06-07 17:59:41.204 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:59:41.219 [info] > git status -z -uall [8ms]
2025-06-07 17:59:41.220 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:59:46.237 [info] > git config --get commit.template [6ms]
2025-06-07 17:59:46.238 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:59:46.249 [info] > git status -z -uall [5ms]
2025-06-07 17:59:46.250 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:59:51.272 [info] > git config --get commit.template [8ms]
2025-06-07 17:59:51.273 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:59:51.286 [info] > git status -z -uall [6ms]
2025-06-07 17:59:51.288 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:59:56.301 [info] > git config --get commit.template [1ms]
2025-06-07 17:59:56.320 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:59:56.334 [info] > git status -z -uall [6ms]
2025-06-07 17:59:56.335 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:00:01.360 [info] > git config --get commit.template [13ms]
2025-06-07 18:00:01.362 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:00:01.375 [info] > git status -z -uall [7ms]
2025-06-07 18:00:01.376 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:00:06.414 [info] > git config --get commit.template [17ms]
2025-06-07 18:00:06.418 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 18:00:06.445 [info] > git status -z -uall [13ms]
2025-06-07 18:00:06.447 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:00:11.471 [info] > git config --get commit.template [9ms]
2025-06-07 18:00:11.473 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:00:11.489 [info] > git status -z -uall [8ms]
2025-06-07 18:00:11.490 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:00:16.516 [info] > git config --get commit.template [11ms]
2025-06-07 18:00:16.519 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:00:16.546 [info] > git status -z -uall [14ms]
2025-06-07 18:00:16.547 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:00:21.578 [info] > git config --get commit.template [13ms]
2025-06-07 18:00:21.580 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:00:21.601 [info] > git status -z -uall [11ms]
2025-06-07 18:00:21.604 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:00:26.622 [info] > git config --get commit.template [0ms]
2025-06-07 18:00:26.637 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:00:26.655 [info] > git status -z -uall [8ms]
2025-06-07 18:00:26.655 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:00:31.684 [info] > git config --get commit.template [11ms]
2025-06-07 18:00:31.685 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:00:31.716 [info] > git status -z -uall [17ms]
2025-06-07 18:00:31.719 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 18:00:36.747 [info] > git config --get commit.template [11ms]
2025-06-07 18:00:36.748 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:00:36.769 [info] > git status -z -uall [9ms]
2025-06-07 18:00:36.771 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 18:00:41.897 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:00:41.897 [info] > git config --get commit.template [21ms]
2025-06-07 18:00:41.948 [info] > git status -z -uall [20ms]
2025-06-07 18:00:41.949 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:00:46.982 [info] > git config --get commit.template [13ms]
2025-06-07 18:00:46.984 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:00:47.007 [info] > git status -z -uall [10ms]
2025-06-07 18:00:47.008 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:00:52.027 [info] > git config --get commit.template [7ms]
2025-06-07 18:00:52.030 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:00:52.047 [info] > git status -z -uall [7ms]
2025-06-07 18:00:52.049 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:00:57.077 [info] > git config --get commit.template [10ms]
2025-06-07 18:00:57.079 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:00:57.098 [info] > git status -z -uall [8ms]
2025-06-07 18:00:57.099 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:01:02.132 [info] > git config --get commit.template [19ms]
2025-06-07 18:01:02.138 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-07 18:01:02.162 [info] > git status -z -uall [12ms]
2025-06-07 18:01:02.166 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:01:07.189 [info] > git config --get commit.template [7ms]
2025-06-07 18:01:07.190 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:01:07.204 [info] > git status -z -uall [8ms]
2025-06-07 18:01:07.205 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:03:06.822 [info] > git config --get commit.template [1ms]
2025-06-07 18:03:06.833 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:03:06.848 [info] > git status -z -uall [7ms]
2025-06-07 18:03:06.849 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:03:11.881 [info] > git config --get commit.template [16ms]
2025-06-07 18:03:11.881 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:03:11.899 [info] > git status -z -uall [7ms]
2025-06-07 18:03:11.899 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:03:16.916 [info] > git config --get commit.template [1ms]
2025-06-07 18:03:16.931 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:03:16.958 [info] > git status -z -uall [13ms]
2025-06-07 18:03:16.959 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 18:03:21.978 [info] > git config --get commit.template [3ms]
2025-06-07 18:03:21.988 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:03:21.999 [info] > git status -z -uall [6ms]
2025-06-07 18:03:22.001 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:03:27.018 [info] > git config --get commit.template [2ms]
2025-06-07 18:03:27.027 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:03:27.047 [info] > git status -z -uall [10ms]
2025-06-07 18:03:27.048 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:03:32.070 [info] > git config --get commit.template [8ms]
2025-06-07 18:03:32.070 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:03:32.086 [info] > git status -z -uall [7ms]
2025-06-07 18:03:32.087 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:03:37.115 [info] > git config --get commit.template [13ms]
2025-06-07 18:03:37.117 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:03:37.146 [info] > git status -z -uall [17ms]
2025-06-07 18:03:37.146 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:03:42.198 [info] > git config --get commit.template [26ms]
2025-06-07 18:03:42.200 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:03:42.248 [info] > git status -z -uall [20ms]
2025-06-07 18:03:42.252 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 18:03:47.289 [info] > git config --get commit.template [17ms]
2025-06-07 18:03:47.289 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:03:47.319 [info] > git status -z -uall [17ms]
2025-06-07 18:03:47.319 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 18:03:52.361 [info] > git config --get commit.template [19ms]
2025-06-07 18:03:52.364 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:03:52.419 [info] > git status -z -uall [29ms]
2025-06-07 18:03:52.420 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:03:57.449 [info] > git config --get commit.template [11ms]
2025-06-07 18:03:57.450 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:03:57.472 [info] > git status -z -uall [12ms]
2025-06-07 18:03:57.473 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:04:02.493 [info] > git config --get commit.template [5ms]
2025-06-07 18:04:02.495 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:04:02.506 [info] > git status -z -uall [5ms]
2025-06-07 18:04:02.507 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:04:07.526 [info] > git config --get commit.template [6ms]
2025-06-07 18:04:07.527 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:04:07.539 [info] > git status -z -uall [6ms]
2025-06-07 18:04:07.540 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:04:12.582 [info] > git config --get commit.template [18ms]
2025-06-07 18:04:12.583 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:04:12.614 [info] > git status -z -uall [16ms]
2025-06-07 18:04:12.615 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:04:17.748 [info] > git config --get commit.template [41ms]
2025-06-07 18:04:17.754 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 18:04:18.049 [info] > git status -z -uall [252ms]
2025-06-07 18:04:18.053 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [208ms]
2025-06-07 18:04:23.078 [info] > git config --get commit.template [11ms]
2025-06-07 18:04:23.079 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:04:23.100 [info] > git status -z -uall [10ms]
2025-06-07 18:04:23.101 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:04:23.595 [info] > git show --textconv :server/routes.ts [20ms]
2025-06-07 18:04:23.601 [info] > git ls-files --stage -- server/routes.ts [8ms]
2025-06-07 18:04:23.634 [info] > git cat-file -s b93bb47e551bcc9e5d022c81e3eccb295c6af454 [6ms]
2025-06-07 18:04:28.135 [info] > git config --get commit.template [19ms]
2025-06-07 18:04:28.136 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:04:28.164 [info] > git status -z -uall [15ms]
2025-06-07 18:04:28.170 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 18:04:33.199 [info] > git config --get commit.template [11ms]
2025-06-07 18:04:33.200 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:04:33.224 [info] > git status -z -uall [12ms]
2025-06-07 18:04:33.224 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 18:04:38.251 [info] > git config --get commit.template [11ms]
2025-06-07 18:04:38.253 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:04:38.282 [info] > git status -z -uall [14ms]
2025-06-07 18:04:38.283 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:04:42.730 [info] > git show --textconv :server/routes.ts [21ms]
2025-06-07 18:04:42.734 [info] > git ls-files --stage -- server/routes.ts [5ms]
2025-06-07 18:04:42.758 [info] > git cat-file -s b93bb47e551bcc9e5d022c81e3eccb295c6af454 [3ms]
2025-06-07 18:04:43.323 [info] > git config --get commit.template [19ms]
2025-06-07 18:04:43.325 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 18:04:43.348 [info] > git status -z -uall [9ms]
2025-06-07 18:04:43.352 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:04:48.379 [info] > git config --get commit.template [9ms]
2025-06-07 18:04:48.380 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:04:48.397 [info] > git status -z -uall [7ms]
2025-06-07 18:04:48.399 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:04:53.419 [info] > git config --get commit.template [7ms]
2025-06-07 18:04:53.420 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:04:53.440 [info] > git status -z -uall [10ms]
2025-06-07 18:04:53.441 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:04:58.462 [info] > git config --get commit.template [8ms]
2025-06-07 18:04:58.463 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:04:58.474 [info] > git status -z -uall [5ms]
2025-06-07 18:04:58.475 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:05:04.256 [info] > git config --get commit.template [2ms]
2025-06-07 18:05:04.271 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:05:04.294 [info] > git status -z -uall [11ms]
2025-06-07 18:05:04.294 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:05:09.315 [info] > git config --get commit.template [8ms]
2025-06-07 18:05:09.316 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:05:09.327 [info] > git status -z -uall [5ms]
2025-06-07 18:05:09.328 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:05:14.350 [info] > git config --get commit.template [10ms]
2025-06-07 18:05:14.351 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:05:14.363 [info] > git status -z -uall [6ms]
2025-06-07 18:05:14.367 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:05:19.386 [info] > git config --get commit.template [7ms]
2025-06-07 18:05:19.388 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:05:19.400 [info] > git status -z -uall [5ms]
2025-06-07 18:05:19.401 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:05:24.424 [info] > git config --get commit.template [9ms]
2025-06-07 18:05:24.425 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:05:24.450 [info] > git status -z -uall [15ms]
2025-06-07 18:05:24.452 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:05:29.476 [info] > git config --get commit.template [10ms]
2025-06-07 18:05:29.477 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:05:29.500 [info] > git status -z -uall [8ms]
2025-06-07 18:05:29.501 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:05:34.526 [info] > git config --get commit.template [11ms]
2025-06-07 18:05:34.527 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:05:34.543 [info] > git status -z -uall [8ms]
2025-06-07 18:05:34.545 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:05:39.566 [info] > git config --get commit.template [8ms]
2025-06-07 18:05:39.567 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:05:39.580 [info] > git status -z -uall [6ms]
2025-06-07 18:05:39.581 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:05:46.367 [info] > git config --get commit.template [6ms]
2025-06-07 18:05:46.368 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:05:46.379 [info] > git status -z -uall [6ms]
2025-06-07 18:05:46.380 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:05:47.858 [info] > git show --textconv :server/routes.ts [58ms]
2025-06-07 18:05:47.860 [info] > git ls-files --stage -- server/routes.ts [55ms]
2025-06-07 18:05:47.912 [info] > git cat-file -s b93bb47e551bcc9e5d022c81e3eccb295c6af454 [28ms]
2025-06-07 18:05:51.410 [info] > git config --get commit.template [16ms]
2025-06-07 18:05:51.411 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:05:51.431 [info] > git status -z -uall [7ms]
2025-06-07 18:05:51.432 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:05:56.447 [info] > git config --get commit.template [5ms]
2025-06-07 18:05:56.449 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:05:56.460 [info] > git status -z -uall [6ms]
2025-06-07 18:05:56.460 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:06:01.478 [info] > git config --get commit.template [8ms]
2025-06-07 18:06:01.478 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:06:01.495 [info] > git status -z -uall [10ms]
2025-06-07 18:06:01.496 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:06:06.516 [info] > git config --get commit.template [6ms]
2025-06-07 18:06:06.518 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:06:06.530 [info] > git status -z -uall [7ms]
2025-06-07 18:06:06.532 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:06:11.571 [info] > git config --get commit.template [12ms]
2025-06-07 18:06:11.572 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:06:11.586 [info] > git status -z -uall [7ms]
2025-06-07 18:06:11.587 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:06:16.621 [info] > git config --get commit.template [16ms]
2025-06-07 18:06:16.622 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:06:16.635 [info] > git status -z -uall [6ms]
2025-06-07 18:06:16.636 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:06:21.706 [info] > git config --get commit.template [56ms]
2025-06-07 18:06:21.719 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:06:21.737 [info] > git status -z -uall [9ms]
2025-06-07 18:06:21.738 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:06:26.760 [info] > git config --get commit.template [9ms]
2025-06-07 18:06:26.761 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:06:26.825 [info] > git status -z -uall [58ms]
2025-06-07 18:06:26.826 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [53ms]
2025-06-07 18:06:31.849 [info] > git config --get commit.template [11ms]
2025-06-07 18:06:31.850 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:06:31.865 [info] > git status -z -uall [7ms]
2025-06-07 18:06:31.866 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:06:36.922 [info] > git config --get commit.template [15ms]
2025-06-07 18:06:36.922 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:06:36.951 [info] > git status -z -uall [10ms]
2025-06-07 18:06:36.952 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:06:41.974 [info] > git config --get commit.template [9ms]
2025-06-07 18:06:41.975 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:06:41.990 [info] > git status -z -uall [7ms]
2025-06-07 18:06:41.992 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 18:06:47.018 [info] > git config --get commit.template [13ms]
2025-06-07 18:06:47.019 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:06:47.042 [info] > git status -z -uall [12ms]
2025-06-07 18:06:47.043 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:06:52.081 [info] > git config --get commit.template [13ms]
2025-06-07 18:06:52.084 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:06:52.107 [info] > git status -z -uall [10ms]
2025-06-07 18:06:52.109 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:06:57.144 [info] > git config --get commit.template [20ms]
2025-06-07 18:06:57.146 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:06:57.166 [info] > git status -z -uall [7ms]
2025-06-07 18:06:57.168 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:07:02.191 [info] > git config --get commit.template [9ms]
2025-06-07 18:07:02.192 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:07:02.209 [info] > git status -z -uall [8ms]
2025-06-07 18:07:02.210 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:07:07.239 [info] > git config --get commit.template [13ms]
2025-06-07 18:07:07.240 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:07:07.257 [info] > git status -z -uall [6ms]
2025-06-07 18:07:07.258 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:07:09.113 [info] > git show --textconv :server/routes.ts [16ms]
2025-06-07 18:07:09.116 [info] > git ls-files --stage -- server/routes.ts [5ms]
2025-06-07 18:07:09.147 [info] > git cat-file -s b93bb47e551bcc9e5d022c81e3eccb295c6af454 [3ms]
2025-06-07 18:07:12.303 [info] > git config --get commit.template [31ms]
2025-06-07 18:07:12.305 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-07 18:07:12.338 [info] > git status -z -uall [15ms]
2025-06-07 18:07:12.341 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:07:17.358 [info] > git config --get commit.template [1ms]
2025-06-07 18:07:17.382 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 18:07:17.419 [info] > git status -z -uall [20ms]
2025-06-07 18:07:17.423 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:07:22.441 [info] > git config --get commit.template [6ms]
2025-06-07 18:07:22.442 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:07:22.453 [info] > git status -z -uall [6ms]
2025-06-07 18:07:22.454 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:07:27.482 [info] > git config --get commit.template [14ms]
2025-06-07 18:07:27.484 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:07:27.512 [info] > git status -z -uall [12ms]
2025-06-07 18:07:27.513 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:07:32.545 [info] > git config --get commit.template [12ms]
2025-06-07 18:07:32.546 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:07:32.562 [info] > git status -z -uall [7ms]
2025-06-07 18:07:32.563 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:07:38.680 [info] > git config --get commit.template [2ms]
2025-06-07 18:07:38.710 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:07:38.728 [info] > git status -z -uall [8ms]
2025-06-07 18:07:38.730 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:07:43.750 [info] > git config --get commit.template [9ms]
2025-06-07 18:07:43.752 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:07:43.771 [info] > git status -z -uall [10ms]
2025-06-07 18:07:43.772 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:07:48.790 [info] > git config --get commit.template [8ms]
2025-06-07 18:07:48.793 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:07:48.808 [info] > git status -z -uall [6ms]
2025-06-07 18:07:48.809 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:07:53.918 [info] > git config --get commit.template [94ms]
2025-06-07 18:07:53.919 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:07:53.940 [info] > git status -z -uall [10ms]
2025-06-07 18:07:53.941 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 18:07:58.962 [info] > git config --get commit.template [7ms]
2025-06-07 18:07:58.963 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:07:58.999 [info] > git status -z -uall [18ms]
2025-06-07 18:07:59.001 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:08:04.021 [info] > git config --get commit.template [7ms]
2025-06-07 18:08:04.022 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:08:04.040 [info] > git status -z -uall [9ms]
2025-06-07 18:08:04.042 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 18:08:09.064 [info] > git config --get commit.template [8ms]
2025-06-07 18:08:09.066 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:08:09.079 [info] > git status -z -uall [6ms]
2025-06-07 18:08:09.081 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
