2025-06-07 18:08:52.908 [info] [main] Log level: Info
2025-06-07 18:08:52.908 [info] [main] Validating found git in: "git"
2025-06-07 18:08:52.908 [info] [main] Using git "2.47.2" from "git"
2025-06-07 18:08:52.908 [info] [Model][doInitialScan] Initial repository scan started
2025-06-07 18:08:52.908 [info] > git rev-parse --show-toplevel [7ms]
2025-06-07 18:08:52.908 [info] > git rev-parse --git-dir --git-common-dir [5ms]
2025-06-07 18:08:52.908 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-07 18:08:52.908 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [13ms]
2025-06-07 18:08:52.909 [info] > git config --get commit.template [30ms]
2025-06-07 18:08:52.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:08:52.909 [info] > git status -z -uall [29ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [37ms]
2025-06-07 18:08:52.909 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [63ms]
2025-06-07 18:08:52.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [98ms]
2025-06-07 18:08:52.909 [info] > git config --get commit.template [12ms]
2025-06-07 18:08:52.909 [info] > git config --get --local branch.main.vscode-merge-base [25ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [11ms]
2025-06-07 18:08:52.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [46ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [31ms]
2025-06-07 18:08:52.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [73ms]
2025-06-07 18:08:52.909 [info] > git merge-base refs/heads/main refs/remotes/origin/main [21ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [234ms]
2025-06-07 18:08:52.909 [info] > git diff --name-status -z --diff-filter=ADMR afcc68bbfc7a9840fe8bc9258ce0aab95444c453...refs/remotes/origin/main [256ms]
2025-06-07 18:08:52.909 [info] > git status -z -uall [32ms]
2025-06-07 18:08:52.909 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [28ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [13ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [13ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [175ms]
2025-06-07 18:08:52.910 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 18:08:52.923 [info] > git rev-parse --show-toplevel [10ms]
2025-06-07 18:08:52.928 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 18:08:52.933 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 18:08:52.938 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 18:08:52.942 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-07 18:08:52.964 [info] > git show --textconv :server/routes.ts [9ms]
2025-06-07 18:08:53.050 [info] > git ls-files --stage -- server/routes.ts [90ms]
2025-06-07 18:08:53.094 [info] > git check-ignore -v -z --stdin [34ms]
2025-06-07 18:08:53.095 [info] > git cat-file -s b93bb47e551bcc9e5d022c81e3eccb295c6af454 [40ms]
2025-06-07 18:08:53.713 [info] > git blame --root --incremental bbfb3d7a212ab0362673f8b8544b7be7984d1e48 -- server/routes.ts [178ms]
2025-06-07 18:09:20.334 [info] > git config --get commit.template [3ms]
2025-06-07 18:09:20.335 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:09:20.341 [info] > git status -z -uall [3ms]
2025-06-07 18:09:20.342 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:09:25.353 [info] > git config --get commit.template [1ms]
2025-06-07 18:09:25.370 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:09:25.394 [info] > git status -z -uall [13ms]
2025-06-07 18:09:25.394 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:09:30.409 [info] > git config --get commit.template [5ms]
2025-06-07 18:09:30.409 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:09:30.418 [info] > git status -z -uall [5ms]
2025-06-07 18:09:30.419 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:09:35.435 [info] > git config --get commit.template [6ms]
2025-06-07 18:09:35.439 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:09:35.472 [info] > git status -z -uall [11ms]
2025-06-07 18:09:35.472 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:09:40.486 [info] > git config --get commit.template [5ms]
2025-06-07 18:09:40.487 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:09:40.495 [info] > git status -z -uall [3ms]
2025-06-07 18:09:40.495 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:09:45.512 [info] > git config --get commit.template [4ms]
2025-06-07 18:09:45.519 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:09:45.534 [info] > git status -z -uall [8ms]
2025-06-07 18:09:45.536 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 18:09:50.558 [info] > git config --get commit.template [9ms]
2025-06-07 18:09:50.559 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:09:50.568 [info] > git status -z -uall [4ms]
2025-06-07 18:09:50.569 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:09:55.583 [info] > git config --get commit.template [4ms]
2025-06-07 18:09:55.583 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:09:55.594 [info] > git status -z -uall [4ms]
2025-06-07 18:09:55.595 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:10:00.608 [info] > git config --get commit.template [5ms]
2025-06-07 18:10:00.609 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:10:00.617 [info] > git status -z -uall [5ms]
2025-06-07 18:10:00.618 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:10:05.635 [info] > git config --get commit.template [8ms]
2025-06-07 18:10:05.636 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:10:05.646 [info] > git status -z -uall [6ms]
2025-06-07 18:10:05.647 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:10:11.634 [info] > git config --get commit.template [8ms]
2025-06-07 18:10:11.637 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:10:11.659 [info] > git status -z -uall [14ms]
2025-06-07 18:10:11.663 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 18:10:16.676 [info] > git config --get commit.template [4ms]
2025-06-07 18:10:16.677 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:10:16.684 [info] > git status -z -uall [3ms]
2025-06-07 18:10:16.686 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:10:21.697 [info] > git config --get commit.template [4ms]
2025-06-07 18:10:21.699 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:10:21.707 [info] > git status -z -uall [5ms]
2025-06-07 18:10:21.708 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:10:26.719 [info] > git config --get commit.template [4ms]
2025-06-07 18:10:26.807 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [88ms]
2025-06-07 18:10:26.826 [info] > git status -z -uall [10ms]
2025-06-07 18:10:26.826 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:10:27.506 [info] > git show --textconv :server/routes.ts [4ms]
2025-06-07 18:10:27.506 [info] > git ls-files --stage -- server/routes.ts [1ms]
2025-06-07 18:10:27.511 [info] > git cat-file -s b93bb47e551bcc9e5d022c81e3eccb295c6af454 [2ms]
2025-06-07 18:10:31.838 [info] > git config --get commit.template [4ms]
2025-06-07 18:10:31.851 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 18:10:31.874 [info] > git status -z -uall [7ms]
2025-06-07 18:10:31.874 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:10:36.889 [info] > git config --get commit.template [6ms]
2025-06-07 18:10:36.890 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:10:36.900 [info] > git status -z -uall [4ms]
2025-06-07 18:10:36.901 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:10:42.638 [info] > git config --get commit.template [5ms]
2025-06-07 18:10:42.639 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:10:42.648 [info] > git status -z -uall [4ms]
2025-06-07 18:10:42.649 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:10:47.660 [info] > git config --get commit.template [4ms]
2025-06-07 18:10:47.661 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:10:47.669 [info] > git status -z -uall [5ms]
2025-06-07 18:10:47.670 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:10:52.688 [info] > git config --get commit.template [9ms]
2025-06-07 18:10:52.691 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:10:52.713 [info] > git status -z -uall [13ms]
2025-06-07 18:10:52.715 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 18:10:57.857 [info] > git config --get commit.template [13ms]
2025-06-07 18:10:57.864 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 18:10:57.896 [info] > git status -z -uall [22ms]
2025-06-07 18:10:57.903 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 18:11:03.053 [info] > git config --get commit.template [38ms]
2025-06-07 18:11:03.073 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [26ms]
2025-06-07 18:11:03.413 [info] > git status -z -uall [324ms]
2025-06-07 18:11:03.418 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [288ms]
2025-06-07 18:11:08.460 [info] > git config --get commit.template [18ms]
2025-06-07 18:11:08.463 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:11:08.492 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 18:11:08.497 [info] > git status -z -uall [13ms]
2025-06-07 18:11:13.517 [info] > git config --get commit.template [6ms]
2025-06-07 18:11:13.521 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:11:13.536 [info] > git status -z -uall [8ms]
2025-06-07 18:11:13.537 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:11:28.234 [info] > git config --get commit.template [17ms]
2025-06-07 18:11:28.237 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-06-07 18:11:28.258 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:11:28.260 [info] > git status -z -uall [14ms]
2025-06-07 18:11:33.431 [info] > git config --get commit.template [22ms]
2025-06-07 18:11:33.432 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 18:11:33.447 [info] > git status -z -uall [5ms]
2025-06-07 18:11:33.448 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:11:38.462 [info] > git config --get commit.template [5ms]
2025-06-07 18:11:38.499 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [32ms]
2025-06-07 18:11:38.508 [info] > git status -z -uall [5ms]
2025-06-07 18:11:38.509 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:11:43.519 [info] > git config --get commit.template [3ms]
2025-06-07 18:11:43.521 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:11:43.527 [info] > git status -z -uall [3ms]
2025-06-07 18:11:43.528 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:11:48.550 [info] > git config --get commit.template [10ms]
2025-06-07 18:11:48.551 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:11:48.574 [info] > git status -z -uall [10ms]
2025-06-07 18:11:48.574 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:12:11.500 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:12:11.500 [info] > git config --get commit.template [9ms]
2025-06-07 18:12:11.511 [info] > git status -z -uall [5ms]
2025-06-07 18:12:11.512 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:12:19.623 [info] > git config --get commit.template [5ms]
2025-06-07 18:12:19.624 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:12:19.635 [info] > git status -z -uall [7ms]
2025-06-07 18:12:19.635 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:12:26.780 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 18:12:26.781 [info] > git config --get commit.template [22ms]
2025-06-07 18:12:26.809 [info] > git status -z -uall [19ms]
2025-06-07 18:12:26.810 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-07 18:12:31.847 [info] > git config --get commit.template [1ms]
2025-06-07 18:12:31.871 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:12:31.901 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 18:12:31.904 [info] > git status -z -uall [14ms]
2025-06-07 18:12:36.918 [info] > git config --get commit.template [1ms]
2025-06-07 18:12:36.927 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:12:36.937 [info] > git status -z -uall [6ms]
2025-06-07 18:12:36.938 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:12:41.957 [info] > git config --get commit.template [0ms]
2025-06-07 18:12:41.965 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:12:41.974 [info] > git status -z -uall [5ms]
2025-06-07 18:12:41.975 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:12:46.984 [info] > git config --get commit.template [2ms]
2025-06-07 18:12:46.992 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:12:47.016 [info] > git status -z -uall [15ms]
2025-06-07 18:12:47.017 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-07 18:12:52.054 [info] > git config --get commit.template [3ms]
2025-06-07 18:12:52.073 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-07 18:12:52.086 [info] > git status -z -uall [7ms]
2025-06-07 18:12:52.087 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:12:57.101 [info] > git config --get commit.template [6ms]
2025-06-07 18:12:57.102 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:12:57.111 [info] > git status -z -uall [5ms]
2025-06-07 18:12:57.112 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:13:02.124 [info] > git config --get commit.template [3ms]
2025-06-07 18:13:02.126 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:13:02.136 [info] > git status -z -uall [5ms]
2025-06-07 18:13:02.137 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:13:07.188 [info] > git config --get commit.template [27ms]
2025-06-07 18:13:07.189 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:13:07.214 [info] > git status -z -uall [20ms]
2025-06-07 18:13:07.215 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [14ms]
2025-06-07 18:13:12.230 [info] > git config --get commit.template [6ms]
2025-06-07 18:13:12.231 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:13:12.240 [info] > git status -z -uall [4ms]
2025-06-07 18:13:12.242 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:13:17.269 [info] > git config --get commit.template [14ms]
2025-06-07 18:13:17.274 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-07 18:13:17.295 [info] > git status -z -uall [12ms]
2025-06-07 18:13:17.296 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 18:13:22.313 [info] > git config --get commit.template [0ms]
2025-06-07 18:13:22.318 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:13:22.336 [info] > git status -z -uall [12ms]
2025-06-07 18:13:22.338 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:13:27.374 [info] > git config --get commit.template [21ms]
2025-06-07 18:13:27.381 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 18:13:27.398 [info] > git status -z -uall [7ms]
2025-06-07 18:13:27.399 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:13:32.412 [info] > git config --get commit.template [4ms]
2025-06-07 18:13:32.413 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:13:32.421 [info] > git status -z -uall [4ms]
2025-06-07 18:13:32.421 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:13:37.434 [info] > git config --get commit.template [1ms]
2025-06-07 18:13:37.444 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:13:37.459 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:13:37.460 [info] > git status -z -uall [8ms]
2025-06-07 18:13:42.509 [info] > git config --get commit.template [6ms]
2025-06-07 18:13:42.510 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:13:42.527 [info] > git status -z -uall [13ms]
2025-06-07 18:13:42.528 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 18:13:48.142 [info] > git config --get commit.template [21ms]
2025-06-07 18:13:48.143 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:13:48.172 [info] > git status -z -uall [12ms]
2025-06-07 18:13:48.177 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 18:13:53.376 [info] > git config --get commit.template [2ms]
2025-06-07 18:13:53.392 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-07 18:13:53.417 [info] > git status -z -uall [16ms]
2025-06-07 18:13:53.422 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
