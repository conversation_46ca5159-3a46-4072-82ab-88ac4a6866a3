2025-06-07 18:08:52.908 [info] [main] Log level: Info
2025-06-07 18:08:52.908 [info] [main] Validating found git in: "git"
2025-06-07 18:08:52.908 [info] [main] Using git "2.47.2" from "git"
2025-06-07 18:08:52.908 [info] [Model][doInitialScan] Initial repository scan started
2025-06-07 18:08:52.908 [info] > git rev-parse --show-toplevel [7ms]
2025-06-07 18:08:52.908 [info] > git rev-parse --git-dir --git-common-dir [5ms]
2025-06-07 18:08:52.908 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-07 18:08:52.908 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [13ms]
2025-06-07 18:08:52.909 [info] > git config --get commit.template [30ms]
2025-06-07 18:08:52.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:08:52.909 [info] > git status -z -uall [29ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [37ms]
2025-06-07 18:08:52.909 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [63ms]
2025-06-07 18:08:52.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [98ms]
2025-06-07 18:08:52.909 [info] > git config --get commit.template [12ms]
2025-06-07 18:08:52.909 [info] > git config --get --local branch.main.vscode-merge-base [25ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [11ms]
2025-06-07 18:08:52.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [46ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [31ms]
2025-06-07 18:08:52.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [73ms]
2025-06-07 18:08:52.909 [info] > git merge-base refs/heads/main refs/remotes/origin/main [21ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [234ms]
2025-06-07 18:08:52.909 [info] > git diff --name-status -z --diff-filter=ADMR afcc68bbfc7a9840fe8bc9258ce0aab95444c453...refs/remotes/origin/main [256ms]
2025-06-07 18:08:52.909 [info] > git status -z -uall [32ms]
2025-06-07 18:08:52.909 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [28ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [13ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [13ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [175ms]
2025-06-07 18:08:52.910 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 18:08:52.923 [info] > git rev-parse --show-toplevel [10ms]
2025-06-07 18:08:52.928 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 18:08:52.933 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 18:08:52.938 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 18:08:52.942 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-07 18:08:52.964 [info] > git show --textconv :server/routes.ts [9ms]
2025-06-07 18:08:53.050 [info] > git ls-files --stage -- server/routes.ts [90ms]
2025-06-07 18:08:53.094 [info] > git check-ignore -v -z --stdin [34ms]
2025-06-07 18:08:53.095 [info] > git cat-file -s b93bb47e551bcc9e5d022c81e3eccb295c6af454 [40ms]
2025-06-07 18:08:53.713 [info] > git blame --root --incremental bbfb3d7a212ab0362673f8b8544b7be7984d1e48 -- server/routes.ts [178ms]
2025-06-07 18:09:20.334 [info] > git config --get commit.template [3ms]
2025-06-07 18:09:20.335 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:09:20.341 [info] > git status -z -uall [3ms]
2025-06-07 18:09:20.342 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:09:25.353 [info] > git config --get commit.template [1ms]
2025-06-07 18:09:25.370 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:09:25.394 [info] > git status -z -uall [13ms]
2025-06-07 18:09:25.394 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:09:30.409 [info] > git config --get commit.template [5ms]
2025-06-07 18:09:30.409 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:09:30.418 [info] > git status -z -uall [5ms]
2025-06-07 18:09:30.419 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:09:35.435 [info] > git config --get commit.template [6ms]
2025-06-07 18:09:35.439 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:09:35.472 [info] > git status -z -uall [11ms]
2025-06-07 18:09:35.472 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:09:40.486 [info] > git config --get commit.template [5ms]
2025-06-07 18:09:40.487 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:09:40.495 [info] > git status -z -uall [3ms]
2025-06-07 18:09:40.495 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:09:45.512 [info] > git config --get commit.template [4ms]
2025-06-07 18:09:45.519 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:09:45.534 [info] > git status -z -uall [8ms]
2025-06-07 18:09:45.536 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 18:09:50.558 [info] > git config --get commit.template [9ms]
2025-06-07 18:09:50.559 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:09:50.568 [info] > git status -z -uall [4ms]
2025-06-07 18:09:50.569 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:09:55.583 [info] > git config --get commit.template [4ms]
2025-06-07 18:09:55.583 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:09:55.594 [info] > git status -z -uall [4ms]
2025-06-07 18:09:55.595 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:10:00.608 [info] > git config --get commit.template [5ms]
2025-06-07 18:10:00.609 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:10:00.617 [info] > git status -z -uall [5ms]
2025-06-07 18:10:00.618 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:10:05.635 [info] > git config --get commit.template [8ms]
2025-06-07 18:10:05.636 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:10:05.646 [info] > git status -z -uall [6ms]
2025-06-07 18:10:05.647 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:10:11.634 [info] > git config --get commit.template [8ms]
2025-06-07 18:10:11.637 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:10:11.659 [info] > git status -z -uall [14ms]
2025-06-07 18:10:11.663 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 18:10:16.676 [info] > git config --get commit.template [4ms]
2025-06-07 18:10:16.677 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:10:16.684 [info] > git status -z -uall [3ms]
2025-06-07 18:10:16.686 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:10:21.697 [info] > git config --get commit.template [4ms]
2025-06-07 18:10:21.699 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:10:21.707 [info] > git status -z -uall [5ms]
2025-06-07 18:10:21.708 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:10:26.719 [info] > git config --get commit.template [4ms]
2025-06-07 18:10:26.807 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [88ms]
2025-06-07 18:10:26.826 [info] > git status -z -uall [10ms]
2025-06-07 18:10:26.826 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:10:27.506 [info] > git show --textconv :server/routes.ts [4ms]
2025-06-07 18:10:27.506 [info] > git ls-files --stage -- server/routes.ts [1ms]
2025-06-07 18:10:27.511 [info] > git cat-file -s b93bb47e551bcc9e5d022c81e3eccb295c6af454 [2ms]
2025-06-07 18:10:31.838 [info] > git config --get commit.template [4ms]
2025-06-07 18:10:31.851 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 18:10:31.874 [info] > git status -z -uall [7ms]
2025-06-07 18:10:31.874 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:10:36.889 [info] > git config --get commit.template [6ms]
2025-06-07 18:10:36.890 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:10:36.900 [info] > git status -z -uall [4ms]
2025-06-07 18:10:36.901 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:10:42.638 [info] > git config --get commit.template [5ms]
2025-06-07 18:10:42.639 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:10:42.648 [info] > git status -z -uall [4ms]
2025-06-07 18:10:42.649 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:10:47.660 [info] > git config --get commit.template [4ms]
2025-06-07 18:10:47.661 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:10:47.669 [info] > git status -z -uall [5ms]
2025-06-07 18:10:47.670 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:10:52.688 [info] > git config --get commit.template [9ms]
2025-06-07 18:10:52.691 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:10:52.713 [info] > git status -z -uall [13ms]
2025-06-07 18:10:52.715 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 18:10:57.857 [info] > git config --get commit.template [13ms]
2025-06-07 18:10:57.864 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 18:10:57.896 [info] > git status -z -uall [22ms]
2025-06-07 18:10:57.903 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 18:11:03.053 [info] > git config --get commit.template [38ms]
2025-06-07 18:11:03.073 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [26ms]
2025-06-07 18:11:03.413 [info] > git status -z -uall [324ms]
2025-06-07 18:11:03.418 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [288ms]
2025-06-07 18:11:08.460 [info] > git config --get commit.template [18ms]
2025-06-07 18:11:08.463 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:11:08.492 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 18:11:08.497 [info] > git status -z -uall [13ms]
2025-06-07 18:11:13.517 [info] > git config --get commit.template [6ms]
2025-06-07 18:11:13.521 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:11:13.536 [info] > git status -z -uall [8ms]
2025-06-07 18:11:13.537 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:11:28.234 [info] > git config --get commit.template [17ms]
2025-06-07 18:11:28.237 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-06-07 18:11:28.258 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:11:28.260 [info] > git status -z -uall [14ms]
2025-06-07 18:11:33.431 [info] > git config --get commit.template [22ms]
2025-06-07 18:11:33.432 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 18:11:33.447 [info] > git status -z -uall [5ms]
2025-06-07 18:11:33.448 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:11:38.462 [info] > git config --get commit.template [5ms]
2025-06-07 18:11:38.499 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [32ms]
2025-06-07 18:11:38.508 [info] > git status -z -uall [5ms]
2025-06-07 18:11:38.509 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:11:43.519 [info] > git config --get commit.template [3ms]
2025-06-07 18:11:43.521 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:11:43.527 [info] > git status -z -uall [3ms]
2025-06-07 18:11:43.528 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:11:48.550 [info] > git config --get commit.template [10ms]
2025-06-07 18:11:48.551 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:11:48.574 [info] > git status -z -uall [10ms]
2025-06-07 18:11:48.574 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:12:11.500 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:12:11.500 [info] > git config --get commit.template [9ms]
2025-06-07 18:12:11.511 [info] > git status -z -uall [5ms]
2025-06-07 18:12:11.512 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:12:19.623 [info] > git config --get commit.template [5ms]
2025-06-07 18:12:19.624 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:12:19.635 [info] > git status -z -uall [7ms]
2025-06-07 18:12:19.635 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:12:26.780 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 18:12:26.781 [info] > git config --get commit.template [22ms]
2025-06-07 18:12:26.809 [info] > git status -z -uall [19ms]
2025-06-07 18:12:26.810 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-07 18:12:31.847 [info] > git config --get commit.template [1ms]
2025-06-07 18:12:31.871 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:12:31.901 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 18:12:31.904 [info] > git status -z -uall [14ms]
2025-06-07 18:12:36.918 [info] > git config --get commit.template [1ms]
2025-06-07 18:12:36.927 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:12:36.937 [info] > git status -z -uall [6ms]
2025-06-07 18:12:36.938 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:12:41.957 [info] > git config --get commit.template [0ms]
2025-06-07 18:12:41.965 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:12:41.974 [info] > git status -z -uall [5ms]
2025-06-07 18:12:41.975 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:12:46.984 [info] > git config --get commit.template [2ms]
2025-06-07 18:12:46.992 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:12:47.016 [info] > git status -z -uall [15ms]
2025-06-07 18:12:47.017 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-07 18:12:52.054 [info] > git config --get commit.template [3ms]
2025-06-07 18:12:52.073 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-07 18:12:52.086 [info] > git status -z -uall [7ms]
2025-06-07 18:12:52.087 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:12:57.101 [info] > git config --get commit.template [6ms]
2025-06-07 18:12:57.102 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:12:57.111 [info] > git status -z -uall [5ms]
2025-06-07 18:12:57.112 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:13:02.124 [info] > git config --get commit.template [3ms]
2025-06-07 18:13:02.126 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:13:02.136 [info] > git status -z -uall [5ms]
2025-06-07 18:13:02.137 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:13:07.188 [info] > git config --get commit.template [27ms]
2025-06-07 18:13:07.189 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:13:07.214 [info] > git status -z -uall [20ms]
2025-06-07 18:13:07.215 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [14ms]
2025-06-07 18:13:12.230 [info] > git config --get commit.template [6ms]
2025-06-07 18:13:12.231 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:13:12.240 [info] > git status -z -uall [4ms]
2025-06-07 18:13:12.242 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:13:17.269 [info] > git config --get commit.template [14ms]
2025-06-07 18:13:17.274 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-07 18:13:17.295 [info] > git status -z -uall [12ms]
2025-06-07 18:13:17.296 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 18:13:22.313 [info] > git config --get commit.template [0ms]
2025-06-07 18:13:22.318 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:13:22.336 [info] > git status -z -uall [12ms]
2025-06-07 18:13:22.338 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:13:27.374 [info] > git config --get commit.template [21ms]
2025-06-07 18:13:27.381 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 18:13:27.398 [info] > git status -z -uall [7ms]
2025-06-07 18:13:27.399 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:13:32.412 [info] > git config --get commit.template [4ms]
2025-06-07 18:13:32.413 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:13:32.421 [info] > git status -z -uall [4ms]
2025-06-07 18:13:32.421 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:13:37.434 [info] > git config --get commit.template [1ms]
2025-06-07 18:13:37.444 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:13:37.459 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:13:37.460 [info] > git status -z -uall [8ms]
2025-06-07 18:13:42.509 [info] > git config --get commit.template [6ms]
2025-06-07 18:13:42.510 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:13:42.527 [info] > git status -z -uall [13ms]
2025-06-07 18:13:42.528 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 18:13:48.142 [info] > git config --get commit.template [21ms]
2025-06-07 18:13:48.143 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:13:48.172 [info] > git status -z -uall [12ms]
2025-06-07 18:13:48.177 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 18:13:53.376 [info] > git config --get commit.template [2ms]
2025-06-07 18:13:53.392 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-07 18:13:53.417 [info] > git status -z -uall [16ms]
2025-06-07 18:13:53.422 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 18:13:58.446 [info] > git config --get commit.template [12ms]
2025-06-07 18:13:58.451 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [12ms]
2025-06-07 18:13:58.461 [info] > git status -z -uall [3ms]
2025-06-07 18:13:58.462 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:14:03.497 [info] > git config --get commit.template [4ms]
2025-06-07 18:14:03.498 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:14:03.507 [info] > git status -z -uall [6ms]
2025-06-07 18:14:03.508 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:14:08.519 [info] > git config --get commit.template [4ms]
2025-06-07 18:14:08.521 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:14:08.529 [info] > git status -z -uall [4ms]
2025-06-07 18:14:08.530 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:14:09.233 [info] > git show --textconv :server/routes.ts [5ms]
2025-06-07 18:14:09.234 [info] > git ls-files --stage -- server/routes.ts [2ms]
2025-06-07 18:14:09.238 [info] > git cat-file -s b93bb47e551bcc9e5d022c81e3eccb295c6af454 [1ms]
2025-06-07 18:14:13.543 [info] > git config --get commit.template [4ms]
2025-06-07 18:14:13.556 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:14:13.572 [info] > git status -z -uall [7ms]
2025-06-07 18:14:13.573 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:14:18.593 [info] > git config --get commit.template [7ms]
2025-06-07 18:14:18.595 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:14:18.607 [info] > git status -z -uall [6ms]
2025-06-07 18:14:18.608 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:16:04.819 [info] > git config --get commit.template [3ms]
2025-06-07 18:16:04.820 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:16:04.829 [info] > git status -z -uall [5ms]
2025-06-07 18:16:04.829 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:16:09.849 [info] > git config --get commit.template [8ms]
2025-06-07 18:16:09.853 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:16:09.866 [info] > git status -z -uall [5ms]
2025-06-07 18:16:09.867 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:17:20.178 [info] > git config --get commit.template [5ms]
2025-06-07 18:17:20.178 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:17:20.187 [info] > git status -z -uall [4ms]
2025-06-07 18:17:20.188 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:17:25.204 [info] > git config --get commit.template [6ms]
2025-06-07 18:17:25.205 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:17:25.223 [info] > git status -z -uall [6ms]
2025-06-07 18:17:25.225 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:17:30.249 [info] > git config --get commit.template [7ms]
2025-06-07 18:17:30.250 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:17:30.262 [info] > git status -z -uall [6ms]
2025-06-07 18:17:30.264 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 18:17:35.279 [info] > git config --get commit.template [5ms]
2025-06-07 18:17:35.280 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:17:35.288 [info] > git status -z -uall [4ms]
2025-06-07 18:17:35.289 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:17:40.307 [info] > git config --get commit.template [6ms]
2025-06-07 18:17:40.310 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:17:40.336 [info] > git status -z -uall [10ms]
2025-06-07 18:17:40.336 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:17:45.353 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:17:45.353 [info] > git config --get commit.template [8ms]
2025-06-07 18:17:45.363 [info] > git status -z -uall [5ms]
2025-06-07 18:17:45.364 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:17:50.376 [info] > git config --get commit.template [4ms]
2025-06-07 18:17:50.377 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:17:50.384 [info] > git status -z -uall [3ms]
2025-06-07 18:17:50.385 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:17:55.409 [info] > git config --get commit.template [8ms]
2025-06-07 18:17:55.410 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:17:55.424 [info] > git status -z -uall [9ms]
2025-06-07 18:17:55.424 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 18:18:00.446 [info] > git config --get commit.template [8ms]
2025-06-07 18:18:00.448 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:18:00.471 [info] > git status -z -uall [13ms]
2025-06-07 18:18:00.477 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-07 18:18:05.512 [info] > git config --get commit.template [18ms]
2025-06-07 18:18:05.513 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:18:05.528 [info] > git status -z -uall [7ms]
2025-06-07 18:18:05.531 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:18:10.541 [info] > git config --get commit.template [0ms]
2025-06-07 18:18:10.549 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:18:10.563 [info] > git status -z -uall [9ms]
2025-06-07 18:18:10.567 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:18:15.615 [info] > git config --get commit.template [4ms]
2025-06-07 18:18:15.616 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:18:15.624 [info] > git status -z -uall [5ms]
2025-06-07 18:18:15.625 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 18:18:20.639 [info] > git config --get commit.template [4ms]
2025-06-07 18:18:20.640 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:18:20.649 [info] > git status -z -uall [6ms]
2025-06-07 18:18:20.649 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:18:25.664 [info] > git config --get commit.template [5ms]
2025-06-07 18:18:25.665 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:18:25.673 [info] > git status -z -uall [4ms]
2025-06-07 18:18:25.674 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:18:30.690 [info] > git config --get commit.template [6ms]
2025-06-07 18:18:30.691 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:18:30.699 [info] > git status -z -uall [5ms]
2025-06-07 18:18:30.700 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:18:35.712 [info] > git config --get commit.template [5ms]
2025-06-07 18:18:35.713 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:18:35.723 [info] > git status -z -uall [5ms]
2025-06-07 18:18:35.724 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:18:40.735 [info] > git config --get commit.template [4ms]
2025-06-07 18:18:40.739 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 18:18:40.748 [info] > git status -z -uall [4ms]
2025-06-07 18:18:40.749 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:18:45.764 [info] > git config --get commit.template [5ms]
2025-06-07 18:18:45.766 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:18:45.774 [info] > git status -z -uall [4ms]
2025-06-07 18:18:45.775 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:18:50.797 [info] > git config --get commit.template [9ms]
2025-06-07 18:18:50.800 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:18:50.810 [info] > git status -z -uall [5ms]
2025-06-07 18:18:50.811 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:18:55.824 [info] > git config --get commit.template [4ms]
2025-06-07 18:18:55.825 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:18:55.835 [info] > git status -z -uall [5ms]
2025-06-07 18:18:55.836 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:19:00.874 [info] > git config --get commit.template [14ms]
2025-06-07 18:19:00.875 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:19:00.889 [info] > git status -z -uall [7ms]
2025-06-07 18:19:00.890 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:19:05.904 [info] > git config --get commit.template [5ms]
2025-06-07 18:19:05.905 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:19:05.914 [info] > git status -z -uall [5ms]
2025-06-07 18:19:05.915 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:19:10.928 [info] > git config --get commit.template [5ms]
2025-06-07 18:19:10.929 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:19:10.936 [info] > git status -z -uall [3ms]
2025-06-07 18:19:10.938 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:19:15.950 [info] > git config --get commit.template [4ms]
2025-06-07 18:19:15.952 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:19:15.959 [info] > git status -z -uall [4ms]
2025-06-07 18:19:15.960 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:19:20.973 [info] > git config --get commit.template [4ms]
2025-06-07 18:19:20.975 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:19:20.983 [info] > git status -z -uall [4ms]
2025-06-07 18:19:20.984 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:19:26.000 [info] > git config --get commit.template [6ms]
2025-06-07 18:19:26.001 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:19:26.009 [info] > git status -z -uall [4ms]
2025-06-07 18:19:26.011 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:19:31.022 [info] > git config --get commit.template [3ms]
2025-06-07 18:19:31.023 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:19:31.030 [info] > git status -z -uall [4ms]
2025-06-07 18:19:31.031 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:19:36.046 [info] > git config --get commit.template [6ms]
2025-06-07 18:19:36.047 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:19:36.058 [info] > git status -z -uall [5ms]
2025-06-07 18:19:36.059 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:19:41.073 [info] > git config --get commit.template [4ms]
2025-06-07 18:19:41.075 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:19:41.083 [info] > git status -z -uall [4ms]
2025-06-07 18:19:41.084 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:19:46.096 [info] > git config --get commit.template [4ms]
2025-06-07 18:19:46.097 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:19:46.104 [info] > git status -z -uall [4ms]
2025-06-07 18:19:46.105 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:19:51.147 [info] > git config --get commit.template [7ms]
2025-06-07 18:19:51.155 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-07 18:19:51.184 [info] > git status -z -uall [16ms]
2025-06-07 18:19:51.198 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [14ms]
2025-06-07 18:19:56.219 [info] > git config --get commit.template [8ms]
2025-06-07 18:19:56.220 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:19:56.241 [info] > git status -z -uall [15ms]
2025-06-07 18:19:56.241 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-07 18:20:01.253 [info] > git config --get commit.template [4ms]
2025-06-07 18:20:01.255 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:20:01.262 [info] > git status -z -uall [3ms]
2025-06-07 18:20:01.263 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:20:06.275 [info] > git config --get commit.template [5ms]
2025-06-07 18:20:06.276 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:20:06.285 [info] > git status -z -uall [4ms]
2025-06-07 18:20:06.286 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:20:11.298 [info] > git config --get commit.template [5ms]
2025-06-07 18:20:11.299 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:20:11.307 [info] > git status -z -uall [4ms]
2025-06-07 18:20:11.308 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:20:16.323 [info] > git config --get commit.template [6ms]
2025-06-07 18:20:16.324 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:20:16.333 [info] > git status -z -uall [5ms]
2025-06-07 18:20:16.334 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:20:21.353 [info] > git config --get commit.template [7ms]
2025-06-07 18:20:21.355 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:20:21.382 [info] > git status -z -uall [10ms]
2025-06-07 18:20:21.385 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 18:20:26.412 [info] > git config --get commit.template [8ms]
2025-06-07 18:20:26.413 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:20:26.420 [info] > git status -z -uall [4ms]
2025-06-07 18:20:26.421 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:20:31.435 [info] > git config --get commit.template [4ms]
2025-06-07 18:20:31.436 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:20:31.444 [info] > git status -z -uall [4ms]
2025-06-07 18:20:31.445 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:20:36.465 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-07 18:20:36.465 [info] > git config --get commit.template [11ms]
2025-06-07 18:20:36.510 [info] > git status -z -uall [36ms]
2025-06-07 18:20:36.513 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:20:41.530 [info] > git config --get commit.template [4ms]
2025-06-07 18:20:41.531 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:20:41.538 [info] > git status -z -uall [3ms]
2025-06-07 18:20:41.539 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:20:46.551 [info] > git config --get commit.template [5ms]
2025-06-07 18:20:46.552 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:20:46.561 [info] > git status -z -uall [5ms]
2025-06-07 18:20:46.561 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:20:51.668 [info] > git config --get commit.template [3ms]
2025-06-07 18:20:51.685 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-07 18:20:51.717 [info] > git status -z -uall [22ms]
2025-06-07 18:20:51.720 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:20:56.734 [info] > git config --get commit.template [4ms]
2025-06-07 18:20:56.735 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:20:56.744 [info] > git status -z -uall [5ms]
2025-06-07 18:20:56.745 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:21:01.757 [info] > git config --get commit.template [4ms]
2025-06-07 18:21:01.758 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:21:01.765 [info] > git status -z -uall [3ms]
2025-06-07 18:21:01.766 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:21:06.781 [info] > git config --get commit.template [6ms]
2025-06-07 18:21:06.787 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-07 18:21:06.808 [info] > git status -z -uall [8ms]
2025-06-07 18:21:06.809 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:21:11.824 [info] > git config --get commit.template [6ms]
2025-06-07 18:21:11.826 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:21:11.837 [info] > git status -z -uall [5ms]
2025-06-07 18:21:11.838 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:21:16.855 [info] > git config --get commit.template [8ms]
2025-06-07 18:21:16.856 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:21:16.865 [info] > git status -z -uall [5ms]
2025-06-07 18:21:16.866 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:21:21.880 [info] > git config --get commit.template [5ms]
2025-06-07 18:21:21.881 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:21:21.889 [info] > git status -z -uall [4ms]
2025-06-07 18:21:21.891 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:21:26.908 [info] > git config --get commit.template [1ms]
2025-06-07 18:21:26.928 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:21:26.939 [info] > git status -z -uall [6ms]
2025-06-07 18:21:26.940 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:21:31.953 [info] > git config --get commit.template [2ms]
2025-06-07 18:21:31.966 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:21:31.993 [info] > git status -z -uall [17ms]
2025-06-07 18:21:31.993 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:21:37.015 [info] > git config --get commit.template [10ms]
2025-06-07 18:21:37.017 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:21:37.030 [info] > git status -z -uall [6ms]
2025-06-07 18:21:37.031 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:21:42.046 [info] > git config --get commit.template [6ms]
2025-06-07 18:21:42.048 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:21:42.061 [info] > git status -z -uall [6ms]
2025-06-07 18:21:42.063 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:21:47.091 [info] > git config --get commit.template [13ms]
2025-06-07 18:21:47.093 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:21:47.110 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:21:47.112 [info] > git status -z -uall [10ms]
2025-06-07 18:21:52.145 [info] > git config --get commit.template [22ms]
2025-06-07 18:21:52.147 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:21:52.160 [info] > git status -z -uall [6ms]
2025-06-07 18:21:52.160 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:21:57.179 [info] > git config --get commit.template [7ms]
2025-06-07 18:21:57.181 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:21:57.197 [info] > git status -z -uall [10ms]
2025-06-07 18:21:57.204 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-07 18:22:02.238 [info] > git config --get commit.template [18ms]
2025-06-07 18:22:02.244 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-07 18:22:02.286 [info] > git status -z -uall [24ms]
2025-06-07 18:22:02.289 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:22:07.311 [info] > git config --get commit.template [10ms]
2025-06-07 18:22:07.320 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-07 18:22:07.360 [info] > git status -z -uall [27ms]
2025-06-07 18:22:07.365 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 18:22:12.388 [info] > git config --get commit.template [11ms]
2025-06-07 18:22:12.388 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:22:12.417 [info] > git status -z -uall [10ms]
2025-06-07 18:22:12.420 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:22:17.432 [info] > git config --get commit.template [4ms]
2025-06-07 18:22:17.433 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:22:17.441 [info] > git status -z -uall [4ms]
2025-06-07 18:22:17.441 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:22:22.454 [info] > git config --get commit.template [2ms]
2025-06-07 18:22:22.463 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:22:22.479 [info] > git status -z -uall [8ms]
2025-06-07 18:22:22.483 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 18:22:27.493 [info] > git config --get commit.template [1ms]
2025-06-07 18:22:27.503 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:22:27.513 [info] > git status -z -uall [4ms]
2025-06-07 18:22:27.514 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:22:32.527 [info] > git config --get commit.template [5ms]
2025-06-07 18:22:32.529 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:22:32.539 [info] > git status -z -uall [5ms]
2025-06-07 18:22:32.540 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:22:37.553 [info] > git config --get commit.template [5ms]
2025-06-07 18:22:37.555 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:22:37.564 [info] > git status -z -uall [5ms]
2025-06-07 18:22:37.565 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:22:42.577 [info] > git config --get commit.template [4ms]
2025-06-07 18:22:42.578 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:22:42.660 [info] > git status -z -uall [77ms]
2025-06-07 18:22:42.662 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [75ms]
2025-06-07 18:22:47.673 [info] > git config --get commit.template [4ms]
2025-06-07 18:22:47.674 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:22:47.681 [info] > git status -z -uall [4ms]
2025-06-07 18:22:47.682 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:22:52.696 [info] > git config --get commit.template [6ms]
2025-06-07 18:22:52.698 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:22:52.707 [info] > git status -z -uall [4ms]
2025-06-07 18:22:52.708 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:22:57.725 [info] > git config --get commit.template [5ms]
2025-06-07 18:22:57.732 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:22:57.751 [info] > git status -z -uall [12ms]
2025-06-07 18:22:57.753 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 18:23:02.771 [info] > git config --get commit.template [7ms]
2025-06-07 18:23:02.773 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:23:02.783 [info] > git status -z -uall [4ms]
2025-06-07 18:23:02.789 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 18:23:07.828 [info] > git config --get commit.template [23ms]
2025-06-07 18:23:07.832 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 18:23:07.879 [info] > git status -z -uall [18ms]
2025-06-07 18:23:07.882 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:23:16.686 [info] > git config --get commit.template [4ms]
2025-06-07 18:23:16.688 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:23:16.704 [info] > git status -z -uall [12ms]
2025-06-07 18:23:16.704 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:25:21.079 [info] > git config --get commit.template [8ms]
2025-06-07 18:25:21.082 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:25:21.096 [info] > git status -z -uall [6ms]
2025-06-07 18:25:21.098 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:25:26.112 [info] > git config --get commit.template [5ms]
2025-06-07 18:25:26.113 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:25:26.121 [info] > git status -z -uall [5ms]
2025-06-07 18:25:26.123 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:25:31.141 [info] > git config --get commit.template [8ms]
2025-06-07 18:25:31.146 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-07 18:25:31.162 [info] > git status -z -uall [9ms]
2025-06-07 18:25:31.163 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:25:36.177 [info] > git config --get commit.template [5ms]
2025-06-07 18:25:36.178 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:25:36.191 [info] > git status -z -uall [9ms]
2025-06-07 18:25:36.191 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:25:41.204 [info] > git config --get commit.template [2ms]
2025-06-07 18:25:41.213 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:25:41.224 [info] > git status -z -uall [5ms]
2025-06-07 18:25:41.225 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:25:54.172 [info] > git config --get commit.template [7ms]
2025-06-07 18:25:54.173 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:25:54.185 [info] > git status -z -uall [5ms]
2025-06-07 18:25:54.185 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:25:59.203 [info] > git config --get commit.template [9ms]
2025-06-07 18:25:59.205 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:25:59.228 [info] > git status -z -uall [8ms]
2025-06-07 18:25:59.228 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:26:04.242 [info] > git config --get commit.template [3ms]
2025-06-07 18:26:04.250 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:26:04.260 [info] > git status -z -uall [5ms]
2025-06-07 18:26:04.262 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:26:09.276 [info] > git config --get commit.template [5ms]
2025-06-07 18:26:09.278 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:26:09.292 [info] > git status -z -uall [8ms]
2025-06-07 18:26:09.294 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:26:14.308 [info] > git config --get commit.template [6ms]
2025-06-07 18:26:14.309 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:26:14.323 [info] > git status -z -uall [7ms]
2025-06-07 18:26:14.325 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:30:43.228 [info] > git config --get commit.template [2ms]
2025-06-07 18:30:43.235 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:30:43.318 [info] > git status -z -uall [77ms]
2025-06-07 18:30:43.319 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [74ms]
2025-06-07 18:31:19.462 [info] > git config --get commit.template [2ms]
2025-06-07 18:31:19.472 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 18:31:19.496 [info] > git status -z -uall [9ms]
2025-06-07 18:31:19.498 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
