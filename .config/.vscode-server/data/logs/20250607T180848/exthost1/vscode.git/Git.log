2025-06-07 18:08:52.908 [info] [main] Log level: Info
2025-06-07 18:08:52.908 [info] [main] Validating found git in: "git"
2025-06-07 18:08:52.908 [info] [main] Using git "2.47.2" from "git"
2025-06-07 18:08:52.908 [info] [Model][doInitialScan] Initial repository scan started
2025-06-07 18:08:52.908 [info] > git rev-parse --show-toplevel [7ms]
2025-06-07 18:08:52.908 [info] > git rev-parse --git-dir --git-common-dir [5ms]
2025-06-07 18:08:52.908 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-07 18:08:52.908 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [13ms]
2025-06-07 18:08:52.909 [info] > git config --get commit.template [30ms]
2025-06-07 18:08:52.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:08:52.909 [info] > git status -z -uall [29ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [37ms]
2025-06-07 18:08:52.909 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [63ms]
2025-06-07 18:08:52.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [98ms]
2025-06-07 18:08:52.909 [info] > git config --get commit.template [12ms]
2025-06-07 18:08:52.909 [info] > git config --get --local branch.main.vscode-merge-base [25ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [11ms]
2025-06-07 18:08:52.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [46ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [31ms]
2025-06-07 18:08:52.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [73ms]
2025-06-07 18:08:52.909 [info] > git merge-base refs/heads/main refs/remotes/origin/main [21ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [234ms]
2025-06-07 18:08:52.909 [info] > git diff --name-status -z --diff-filter=ADMR afcc68bbfc7a9840fe8bc9258ce0aab95444c453...refs/remotes/origin/main [256ms]
2025-06-07 18:08:52.909 [info] > git status -z -uall [32ms]
2025-06-07 18:08:52.909 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [28ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [13ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [13ms]
2025-06-07 18:08:52.909 [info] > git rev-parse --show-toplevel [175ms]
2025-06-07 18:08:52.910 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 18:08:52.923 [info] > git rev-parse --show-toplevel [10ms]
2025-06-07 18:08:52.928 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 18:08:52.933 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 18:08:52.938 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 18:08:52.942 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-07 18:08:52.964 [info] > git show --textconv :server/routes.ts [9ms]
2025-06-07 18:08:53.050 [info] > git ls-files --stage -- server/routes.ts [90ms]
2025-06-07 18:08:53.094 [info] > git check-ignore -v -z --stdin [34ms]
2025-06-07 18:08:53.095 [info] > git cat-file -s b93bb47e551bcc9e5d022c81e3eccb295c6af454 [40ms]
2025-06-07 18:08:53.713 [info] > git blame --root --incremental bbfb3d7a212ab0362673f8b8544b7be7984d1e48 -- server/routes.ts [178ms]
2025-06-07 18:09:20.334 [info] > git config --get commit.template [3ms]
2025-06-07 18:09:20.335 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:09:20.341 [info] > git status -z -uall [3ms]
2025-06-07 18:09:20.342 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:09:25.353 [info] > git config --get commit.template [1ms]
2025-06-07 18:09:25.370 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:09:25.394 [info] > git status -z -uall [13ms]
2025-06-07 18:09:25.394 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:09:30.409 [info] > git config --get commit.template [5ms]
2025-06-07 18:09:30.409 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:09:30.418 [info] > git status -z -uall [5ms]
2025-06-07 18:09:30.419 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:09:35.435 [info] > git config --get commit.template [6ms]
2025-06-07 18:09:35.439 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:09:35.472 [info] > git status -z -uall [11ms]
2025-06-07 18:09:35.472 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:09:40.486 [info] > git config --get commit.template [5ms]
2025-06-07 18:09:40.487 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:09:40.495 [info] > git status -z -uall [3ms]
2025-06-07 18:09:40.495 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:09:45.512 [info] > git config --get commit.template [4ms]
2025-06-07 18:09:45.519 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 18:09:45.534 [info] > git status -z -uall [8ms]
2025-06-07 18:09:45.536 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 18:09:50.558 [info] > git config --get commit.template [9ms]
2025-06-07 18:09:50.559 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:09:50.568 [info] > git status -z -uall [4ms]
2025-06-07 18:09:50.569 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:09:55.583 [info] > git config --get commit.template [4ms]
2025-06-07 18:09:55.583 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:09:55.594 [info] > git status -z -uall [4ms]
2025-06-07 18:09:55.595 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 18:10:00.608 [info] > git config --get commit.template [5ms]
2025-06-07 18:10:00.609 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:10:00.617 [info] > git status -z -uall [5ms]
2025-06-07 18:10:00.618 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:10:05.635 [info] > git config --get commit.template [8ms]
2025-06-07 18:10:05.636 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:10:05.646 [info] > git status -z -uall [6ms]
2025-06-07 18:10:05.647 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:10:11.634 [info] > git config --get commit.template [8ms]
2025-06-07 18:10:11.637 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 18:10:11.659 [info] > git status -z -uall [14ms]
2025-06-07 18:10:11.663 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 18:10:16.676 [info] > git config --get commit.template [4ms]
2025-06-07 18:10:16.677 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 18:10:16.684 [info] > git status -z -uall [3ms]
2025-06-07 18:10:16.686 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:10:21.697 [info] > git config --get commit.template [4ms]
2025-06-07 18:10:21.699 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:10:21.707 [info] > git status -z -uall [5ms]
2025-06-07 18:10:21.708 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:10:26.719 [info] > git config --get commit.template [4ms]
2025-06-07 18:10:26.807 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [88ms]
2025-06-07 18:10:26.826 [info] > git status -z -uall [10ms]
2025-06-07 18:10:26.826 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 18:10:27.506 [info] > git show --textconv :server/routes.ts [4ms]
2025-06-07 18:10:27.506 [info] > git ls-files --stage -- server/routes.ts [1ms]
2025-06-07 18:10:27.511 [info] > git cat-file -s b93bb47e551bcc9e5d022c81e3eccb295c6af454 [2ms]
2025-06-07 18:10:31.838 [info] > git config --get commit.template [4ms]
2025-06-07 18:10:31.851 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 18:10:31.874 [info] > git status -z -uall [7ms]
2025-06-07 18:10:31.874 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 18:10:36.889 [info] > git config --get commit.template [6ms]
2025-06-07 18:10:36.890 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 18:10:36.900 [info] > git status -z -uall [4ms]
2025-06-07 18:10:36.901 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
