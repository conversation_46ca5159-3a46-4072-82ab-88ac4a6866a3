import { Link, useLocation } from "wouter";
import { cn } from "@/lib/utils";

export default function MobileNav() {
  const [location] = useLocation();

  const isLinkActive = (path: string) => {
    if (path === "/" && location === "/") return true;
    if (path !== "/" && location.startsWith(path)) return true;
    return false;
  };

  const navItems = [
    { path: "/", icon: "dashboard", label: "Dashboard" },
    { path: "/clients", icon: "people", label: "Clients" },
    { path: "/notes", icon: "description", label: "Notes" },
    { path: "/settings", icon: "more_horiz", label: "More" },
  ];

  return (
    <div className="block md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-10">
      <div className="flex justify-around">
        {navItems.map((item) => (
          <Link key={item.path} href={item.path}>
            <div
              className={cn(
                "flex flex-col items-center py-2 cursor-pointer",
                isLinkActive(item.path) ? "text-primary" : "text-neutral-dark hover:text-primary"
              )}
            >
              <span className="material-icons">{item.icon}</span>
              <span className="text-xs">{item.label}</span>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}
