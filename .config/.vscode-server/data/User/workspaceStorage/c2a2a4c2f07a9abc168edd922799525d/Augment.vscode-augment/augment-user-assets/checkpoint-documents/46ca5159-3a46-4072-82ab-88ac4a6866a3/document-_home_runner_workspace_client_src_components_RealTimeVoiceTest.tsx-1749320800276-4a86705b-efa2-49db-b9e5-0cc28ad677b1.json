{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RealTimeVoiceTest.tsx"}, "originalCode": "import React, { useState, useEffect, useRef } from 'react';\nimport { VoiceConfig, ConversationConfig } from '@shared/schema';\n\ninterface Metrics {\n  latency: number;\n  audioQuality: number;\n  conversationFlow: number;\n  responseTime: number;\n}\n\ninterface RealTimeVoiceTestProps {\n  onConfigChange: (config: { voice: VoiceConfig; conversation: ConversationConfig }) => void;\n}\n\nexport const RealTimeVoiceTest: React.FC<RealTimeVoiceTestProps> = ({ onConfigChange }) => {\n  const [isConnected, setIsConnected] = useState(false);\n  const [isRecording, setIsRecording] = useState(false);\n  const [metrics, setMetrics] = useState<Metrics>({\n    latency: 0,\n    audioQuality: 0,\n    conversationFlow: 0,\n    responseTime: 0\n  });\n  const [voiceConfig, setVoiceConfig] = useState<VoiceConfig>({\n    voice: \"shimmer\",\n    speed: 1.05,\n    pitch: 1.0,\n    emphasis: 'moderate',\n    prosody: {\n      emotionalRange: 0.6,\n      questionInflection: 0.7,\n      pauseDuration: 300\n    }\n  });\n  const [conversationConfig, setConversationConfig] = useState<ConversationConfig>({\n    temperature: 0.8,\n    maxTokens: 1000,\n    presencePenalty: 0.1,\n    frequencyPenalty: 0.1,\n    turnTaking: {\n      backchannelFrequency: 0.3,\n      minSilenceDuration: 300,\n      maxInterruptionGap: 500\n    },\n    responseStyle: {\n      minResponseLength: 50,\n      maxResponseLength: 150,\n      temperature: 0.8,\n      presencePenalty: 0.1,\n      frequencyPenalty: 0.1\n    }\n  });\n\n  const wsRef = useRef<WebSocket | null>(null);\n  const audioContextRef = useRef<AudioContext | null>(null);\n  const mediaRecorderRef = useRef<MediaRecorder | null>(null);\n  const audioChunksRef = useRef<Blob[]>([]);\n\n  useEffect(() => {\n    // Initialize WebSocket connection\n    const ws = new WebSocket('ws://localhost:3000/ws');\n    wsRef.current = ws;\n\n    ws.onopen = () => {\n      setIsConnected(true);\n      console.log('WebSocket connected');\n    };\n\n    ws.onmessage = (event) => {\n      const data = JSON.parse(event.data);\n      handleWebSocketMessage(data);\n    };\n\n    ws.onclose = () => {\n      setIsConnected(false);\n      console.log('WebSocket disconnected');\n    };\n\n    return () => {\n      ws.close();\n    };\n  }, []);\n\n  const handleWebSocketMessage = (data: any) => {\n    switch (data.type) {\n      case 'metrics_update':\n        setMetrics(prev => ({\n          ...prev,\n          latency: data.metrics.latency,\n          responseTime: data.metrics.responseTime\n        }));\n        break;\n      case 'audio_chunk':\n        playAudioChunk(data.audioData);\n        break;\n      case 'error':\n        console.error('WebSocket error:', data.message);\n        break;\n    }\n  };\n\n  const playAudioChunk = (base64Audio: string) => {\n    if (!audioContextRef.current) {\n      audioContextRef.current = new AudioContext();\n    }\n\n    const audioData = Uint8Array.from(atob(base64Audio), c => c.charCodeAt(0));\n    audioContextRef.current.decodeAudioData(audioData.buffer, (buffer) => {\n      const source = audioContextRef.current!.createBufferSource();\n      source.buffer = buffer;\n      source.connect(audioContextRef.current!.destination);\n      source.start();\n    });\n  };\n\n  const startRecording = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n      const mediaRecorder = new MediaRecorder(stream);\n      mediaRecorderRef.current = mediaRecorder;\n      audioChunksRef.current = [];\n\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n          // Send audio chunk to server\n          wsRef.current?.send(JSON.stringify({\n            type: 'audio',\n            audioData: event.data\n          }));\n        }\n      };\n\n      mediaRecorder.start(100); // Capture audio in 100ms chunks\n      setIsRecording(true);\n    } catch (error) {\n      console.error('Error starting recording:', error);\n    }\n  };\n\n  const stopRecording = () => {\n    if (mediaRecorderRef.current) {\n      mediaRecorderRef.current.stop();\n      setIsRecording(false);\n    }\n  };\n\n  const handleVoiceConfigChange = (key: keyof VoiceConfig, value: any) => {\n    const newConfig = { ...voiceConfig, [key]: value };\n    setVoiceConfig(newConfig);\n    onConfigChange({ voice: newConfig, conversation: conversationConfig });\n  };\n\n  const handleConversationConfigChange = (key: keyof ConversationConfig, value: any) => {\n    const newConfig = { ...conversationConfig, [key]: value };\n    setConversationConfig(newConfig);\n    onConfigChange({ voice: voiceConfig, conversation: newConfig });\n  };\n\n  return (\n    <div className=\"p-4 space-y-4\">\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-xl font-bold\">Real-Time Voice Test</h2>\n        <div className=\"flex items-center space-x-2\">\n          <span className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />\n          <span>{isConnected ? 'Connected' : 'Disconnected'}</span>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        {/* Voice Configuration */}\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-semibold\">Voice Configuration</h3>\n          <div className=\"space-y-2\">\n            <label className=\"block\">\n              Voice:\n              <select\n                value={voiceConfig.voice}\n                onChange={(e) => handleVoiceConfigChange('voice', e.target.value)}\n                className=\"w-full p-2 border rounded\"\n              >\n                <option value=\"shimmer\">Shimmer</option>\n                <option value=\"alloy\">Alloy</option>\n                <option value=\"echo\">Echo</option>\n                <option value=\"fable\">Fable</option>\n                <option value=\"onyx\">Onyx</option>\n                <option value=\"nova\">Nova</option>\n              </select>\n            </label>\n\n            <label className=\"block\">\n              Speed:\n              <input\n                type=\"range\"\n                min=\"0.5\"\n                max=\"2\"\n                step=\"0.05\"\n                value={voiceConfig.speed}\n                onChange={(e) => handleVoiceConfigChange('speed', parseFloat(e.target.value))}\n                className=\"w-full\"\n              />\n              <span>{voiceConfig.speed.toFixed(2)}x</span>\n            </label>\n\n            <label className=\"block\">\n              Emphasis:\n              <select\n                value={voiceConfig.emphasis}\n                onChange={(e) => handleVoiceConfigChange('emphasis', e.target.value)}\n                className=\"w-full p-2 border rounded\"\n              >\n                <option value=\"strong\">Strong</option>\n                <option value=\"moderate\">Moderate</option>\n                <option value=\"subtle\">Subtle</option>\n              </select>\n            </label>\n          </div>\n        </div>\n\n        {/* Conversation Configuration */}\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-semibold\">Conversation Configuration</h3>\n          <div className=\"space-y-2\">\n            <label className=\"block\">\n              Backchannel Frequency:\n              <input\n                type=\"range\"\n                min=\"0\"\n                max=\"1\"\n                step=\"0.1\"\n                value={conversationConfig.turnTaking.backchannelFrequency}\n                onChange={(e) => handleConversationConfigChange('turnTaking', {\n                  ...conversationConfig.turnTaking,\n                  backchannelFrequency: parseFloat(e.target.value)\n                })}\n                className=\"w-full\"\n              />\n              <span>{(conversationConfig.turnTaking.backchannelFrequency * 100).toFixed(0)}%</span>\n            </label>\n\n            <label className=\"block\">\n              Min Silence Duration:\n              <input\n                type=\"range\"\n                min=\"100\"\n                max=\"1000\"\n                step=\"50\"\n                value={conversationConfig.turnTaking.minSilenceDuration}\n                onChange={(e) => handleConversationConfigChange('turnTaking', {\n                  ...conversationConfig.turnTaking,\n                  minSilenceDuration: parseInt(e.target.value)\n                })}\n                className=\"w-full\"\n              />\n              <span>{conversationConfig.turnTaking.minSilenceDuration}ms</span>\n            </label>\n\n            <label className=\"block\">\n              Response Temperature:\n              <input\n                type=\"range\"\n                min=\"0\"\n                max=\"1\"\n                step=\"0.1\"\n                value={conversationConfig.responseStyle.temperature}\n                onChange={(e) => handleConversationConfigChange('responseStyle', {\n                  ...conversationConfig.responseStyle,\n                  temperature: parseFloat(e.target.value)\n                })}\n                className=\"w-full\"\n              />\n              <span>{conversationConfig.responseStyle.temperature.toFixed(1)}</span>\n            </label>\n          </div>\n        </div>\n      </div>\n\n      {/* Metrics Display */}\n      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n        <div className=\"p-4 bg-gray-100 rounded\">\n          <h4 className=\"font-semibold\">Latency</h4>\n          <p className=\"text-2xl\">{metrics.latency}ms</p>\n        </div>\n        <div className=\"p-4 bg-gray-100 rounded\">\n          <h4 className=\"font-semibold\">Audio Quality</h4>\n          <p className=\"text-2xl\">{metrics.audioQuality}/10</p>\n        </div>\n        <div className=\"p-4 bg-gray-100 rounded\">\n          <h4 className=\"font-semibold\">Conversation Flow</h4>\n          <p className=\"text-2xl\">{metrics.conversationFlow}/10</p>\n        </div>\n        <div className=\"p-4 bg-gray-100 rounded\">\n          <h4 className=\"font-semibold\">Response Time</h4>\n          <p className=\"text-2xl\">{metrics.responseTime}ms</p>\n        </div>\n      </div>\n\n      {/* Controls */}\n      <div className=\"flex justify-center space-x-4\">\n        <button\n          onClick={isRecording ? stopRecording : startRecording}\n          className={`px-4 py-2 rounded ${\n            isRecording ? 'bg-red-500 hover:bg-red-600' : 'bg-blue-500 hover:bg-blue-600'\n          } text-white`}\n        >\n          {isRecording ? 'Stop Recording' : 'Start Recording'}\n        </button>\n      </div>\n    </div>\n  );\n}; ", "modifiedCode": "import React, { useState, useEffect, useRef } from 'react';\nimport { VoiceConfig, ConversationConfig } from '@shared/schema';\n\ninterface Metrics {\n  latency: number;\n  audioQuality: number;\n  conversationFlow: number;\n  responseTime: number;\n}\n\ninterface RealTimeVoiceTestProps {\n  onConfigChange: (config: { voice: VoiceConfig; conversation: ConversationConfig }) => void;\n}\n\nexport const RealTimeVoiceTest: React.FC<RealTimeVoiceTestProps> = ({ onConfigChange }) => {\n  const [isConnected, setIsConnected] = useState(false);\n  const [isRecording, setIsRecording] = useState(false);\n  const [metrics, setMetrics] = useState<Metrics>({\n    latency: 0,\n    audioQuality: 0,\n    conversationFlow: 0,\n    responseTime: 0\n  });\n  const [voiceConfig, setVoiceConfig] = useState<VoiceConfig>({\n    voice: \"shimmer\",\n    speed: 1.05,\n    pitch: 1.0,\n    emphasis: 'moderate',\n    prosody: {\n      emotionalRange: 0.6,\n      questionInflection: 0.7,\n      pauseDuration: 300\n    }\n  });\n  const [conversationConfig, setConversationConfig] = useState<ConversationConfig>({\n    temperature: 0.8,\n    maxTokens: 1000,\n    presencePenalty: 0.1,\n    frequencyPenalty: 0.1,\n    turnTaking: {\n      backchannelFrequency: 0.3,\n      minSilenceDuration: 300,\n      maxInterruptionGap: 500\n    },\n    responseStyle: {\n      minResponseLength: 50,\n      maxResponseLength: 150,\n      temperature: 0.8,\n      presencePenalty: 0.1,\n      frequencyPenalty: 0.1\n    }\n  });\n\n  const wsRef = useRef<WebSocket | null>(null);\n  const audioContextRef = useRef<AudioContext | null>(null);\n  const mediaRecorderRef = useRef<MediaRecorder | null>(null);\n  const audioChunksRef = useRef<Blob[]>([]);\n\n  useEffect(() => {\n    // Initialize WebSocket connection\n    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';\n    const wsUrl = `${protocol}//${window.location.host}/ws`;\n    const ws = new WebSocket(wsUrl);\n    wsRef.current = ws;\n\n    ws.onopen = () => {\n      setIsConnected(true);\n      console.log('WebSocket connected');\n    };\n\n    ws.onmessage = (event) => {\n      const data = JSON.parse(event.data);\n      handleWebSocketMessage(data);\n    };\n\n    ws.onclose = () => {\n      setIsConnected(false);\n      console.log('WebSocket disconnected');\n    };\n\n    return () => {\n      ws.close();\n    };\n  }, []);\n\n  const handleWebSocketMessage = (data: any) => {\n    switch (data.type) {\n      case 'metrics_update':\n        setMetrics(prev => ({\n          ...prev,\n          latency: data.metrics.latency,\n          responseTime: data.metrics.responseTime\n        }));\n        break;\n      case 'audio_chunk':\n        playAudioChunk(data.audioData);\n        break;\n      case 'error':\n        console.error('WebSocket error:', data.message);\n        break;\n    }\n  };\n\n  const playAudioChunk = (base64Audio: string) => {\n    if (!audioContextRef.current) {\n      audioContextRef.current = new AudioContext();\n    }\n\n    const audioData = Uint8Array.from(atob(base64Audio), c => c.charCodeAt(0));\n    audioContextRef.current.decodeAudioData(audioData.buffer, (buffer) => {\n      const source = audioContextRef.current!.createBufferSource();\n      source.buffer = buffer;\n      source.connect(audioContextRef.current!.destination);\n      source.start();\n    });\n  };\n\n  const startRecording = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n      const mediaRecorder = new MediaRecorder(stream);\n      mediaRecorderRef.current = mediaRecorder;\n      audioChunksRef.current = [];\n\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n          // Send audio chunk to server\n          wsRef.current?.send(JSON.stringify({\n            type: 'audio',\n            audioData: event.data\n          }));\n        }\n      };\n\n      mediaRecorder.start(100); // Capture audio in 100ms chunks\n      setIsRecording(true);\n    } catch (error) {\n      console.error('Error starting recording:', error);\n    }\n  };\n\n  const stopRecording = () => {\n    if (mediaRecorderRef.current) {\n      mediaRecorderRef.current.stop();\n      setIsRecording(false);\n    }\n  };\n\n  const handleVoiceConfigChange = (key: keyof VoiceConfig, value: any) => {\n    const newConfig = { ...voiceConfig, [key]: value };\n    setVoiceConfig(newConfig);\n    onConfigChange({ voice: newConfig, conversation: conversationConfig });\n  };\n\n  const handleConversationConfigChange = (key: keyof ConversationConfig, value: any) => {\n    const newConfig = { ...conversationConfig, [key]: value };\n    setConversationConfig(newConfig);\n    onConfigChange({ voice: voiceConfig, conversation: newConfig });\n  };\n\n  return (\n    <div className=\"p-4 space-y-4\">\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-xl font-bold\">Real-Time Voice Test</h2>\n        <div className=\"flex items-center space-x-2\">\n          <span className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />\n          <span>{isConnected ? 'Connected' : 'Disconnected'}</span>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        {/* Voice Configuration */}\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-semibold\">Voice Configuration</h3>\n          <div className=\"space-y-2\">\n            <label className=\"block\">\n              Voice:\n              <select\n                value={voiceConfig.voice}\n                onChange={(e) => handleVoiceConfigChange('voice', e.target.value)}\n                className=\"w-full p-2 border rounded\"\n              >\n                <option value=\"shimmer\">Shimmer</option>\n                <option value=\"alloy\">Alloy</option>\n                <option value=\"echo\">Echo</option>\n                <option value=\"fable\">Fable</option>\n                <option value=\"onyx\">Onyx</option>\n                <option value=\"nova\">Nova</option>\n              </select>\n            </label>\n\n            <label className=\"block\">\n              Speed:\n              <input\n                type=\"range\"\n                min=\"0.5\"\n                max=\"2\"\n                step=\"0.05\"\n                value={voiceConfig.speed}\n                onChange={(e) => handleVoiceConfigChange('speed', parseFloat(e.target.value))}\n                className=\"w-full\"\n              />\n              <span>{voiceConfig.speed.toFixed(2)}x</span>\n            </label>\n\n            <label className=\"block\">\n              Emphasis:\n              <select\n                value={voiceConfig.emphasis}\n                onChange={(e) => handleVoiceConfigChange('emphasis', e.target.value)}\n                className=\"w-full p-2 border rounded\"\n              >\n                <option value=\"strong\">Strong</option>\n                <option value=\"moderate\">Moderate</option>\n                <option value=\"subtle\">Subtle</option>\n              </select>\n            </label>\n          </div>\n        </div>\n\n        {/* Conversation Configuration */}\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-semibold\">Conversation Configuration</h3>\n          <div className=\"space-y-2\">\n            <label className=\"block\">\n              Backchannel Frequency:\n              <input\n                type=\"range\"\n                min=\"0\"\n                max=\"1\"\n                step=\"0.1\"\n                value={conversationConfig.turnTaking.backchannelFrequency}\n                onChange={(e) => handleConversationConfigChange('turnTaking', {\n                  ...conversationConfig.turnTaking,\n                  backchannelFrequency: parseFloat(e.target.value)\n                })}\n                className=\"w-full\"\n              />\n              <span>{(conversationConfig.turnTaking.backchannelFrequency * 100).toFixed(0)}%</span>\n            </label>\n\n            <label className=\"block\">\n              Min Silence Duration:\n              <input\n                type=\"range\"\n                min=\"100\"\n                max=\"1000\"\n                step=\"50\"\n                value={conversationConfig.turnTaking.minSilenceDuration}\n                onChange={(e) => handleConversationConfigChange('turnTaking', {\n                  ...conversationConfig.turnTaking,\n                  minSilenceDuration: parseInt(e.target.value)\n                })}\n                className=\"w-full\"\n              />\n              <span>{conversationConfig.turnTaking.minSilenceDuration}ms</span>\n            </label>\n\n            <label className=\"block\">\n              Response Temperature:\n              <input\n                type=\"range\"\n                min=\"0\"\n                max=\"1\"\n                step=\"0.1\"\n                value={conversationConfig.responseStyle.temperature}\n                onChange={(e) => handleConversationConfigChange('responseStyle', {\n                  ...conversationConfig.responseStyle,\n                  temperature: parseFloat(e.target.value)\n                })}\n                className=\"w-full\"\n              />\n              <span>{conversationConfig.responseStyle.temperature.toFixed(1)}</span>\n            </label>\n          </div>\n        </div>\n      </div>\n\n      {/* Metrics Display */}\n      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n        <div className=\"p-4 bg-gray-100 rounded\">\n          <h4 className=\"font-semibold\">Latency</h4>\n          <p className=\"text-2xl\">{metrics.latency}ms</p>\n        </div>\n        <div className=\"p-4 bg-gray-100 rounded\">\n          <h4 className=\"font-semibold\">Audio Quality</h4>\n          <p className=\"text-2xl\">{metrics.audioQuality}/10</p>\n        </div>\n        <div className=\"p-4 bg-gray-100 rounded\">\n          <h4 className=\"font-semibold\">Conversation Flow</h4>\n          <p className=\"text-2xl\">{metrics.conversationFlow}/10</p>\n        </div>\n        <div className=\"p-4 bg-gray-100 rounded\">\n          <h4 className=\"font-semibold\">Response Time</h4>\n          <p className=\"text-2xl\">{metrics.responseTime}ms</p>\n        </div>\n      </div>\n\n      {/* Controls */}\n      <div className=\"flex justify-center space-x-4\">\n        <button\n          onClick={isRecording ? stopRecording : startRecording}\n          className={`px-4 py-2 rounded ${\n            isRecording ? 'bg-red-500 hover:bg-red-600' : 'bg-blue-500 hover:bg-blue-600'\n          } text-white`}\n        >\n          {isRecording ? 'Stop Recording' : 'Start Recording'}\n        </button>\n      </div>\n    </div>\n  );\n}; "}