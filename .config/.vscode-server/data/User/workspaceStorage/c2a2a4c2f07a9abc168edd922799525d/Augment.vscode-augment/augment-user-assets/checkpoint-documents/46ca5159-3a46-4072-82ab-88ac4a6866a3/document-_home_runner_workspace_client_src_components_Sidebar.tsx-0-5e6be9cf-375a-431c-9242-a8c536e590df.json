{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Sidebar.tsx"}, "originalCode": "import { Link, useLocation } from \"wouter\";\nimport { cn } from \"@/lib/utils\";\nimport { User } from \"@shared/schema\";\nimport { useAuth } from \"@/hooks/use-auth\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport logoImage from \"../assets/lamaMind-logo.png\";\n\ninterface SidebarProps {\n  user?: User;\n  className?: string;\n  onLinkClick?: () => void;\n}\n\nexport default function Sidebar({ user, className, onLinkClick }: SidebarProps) {\n  const [location, setLocation] = useLocation();\n  const { logoutMutation } = useAuth();\n  const { toast } = useToast();\n\n  const isLinkActive = (path: string) => {\n    if (path === \"/\" && location === \"/\") return true;\n    if (path !== \"/\" && location.startsWith(path)) return true;\n    return false;\n  };\n\n  // Determine which navigation items to show based on user role\n  const baseNavItems = [\n    { path: \"/dashboard\", icon: \"dashboard\", label: \"Dashboard\" },\n    { path: \"/settings\", icon: \"settings\", label: \"Settings\" },\n  ];\n  \n  // Add role-specific navigation items\n  let navItems = [];\n  \n  if (user?.userRole === 'client') {\n    navItems = [\n      ...baseNavItems,\n      { path: \"/voice-therapy\", icon: \"mic\", label: \"Voice Therapy\" },\n    ];\n  } else if (user?.userRole === 'admin') {\n    navItems = [\n      ...baseNavItems,\n      { path: \"/clients\", icon: \"people\", label: \"Clients\" },\n      { path: \"/notes\", icon: \"description\", label: \"Session Notes\" },\n      { path: \"/analytics\", icon: \"insights\", label: \"Analytics\" },\n      { path: \"/admin-dashboard\", icon: \"admin_panel_settings\", label: \"Admin Dashboard\" },\n      { path: \"/admin-ai-test\", icon: \"psychology\", label: \"AI Test Center\" },\n    ];\n  } else {\n    // Default for doctor role\n    navItems = [\n      ...baseNavItems,\n      { path: \"/clients\", icon: \"people\", label: \"Clients\" },\n      { path: \"/notes\", icon: \"description\", label: \"Session Notes\" },\n      { path: \"/analytics\", icon: \"insights\", label: \"Analytics\" },\n    ];\n  }\n\n  const handleClick = () => {\n    if (onLinkClick) onLinkClick();\n  };\n  \n  const handleLogout = () => {\n    logoutMutation.mutate(undefined, {\n      onSuccess: () => {\n        toast({\n          title: \"Logged out successfully\",\n          description: \"You have been logged out of your account.\",\n        });\n        setLocation(\"/auth\");\n      },\n      onError: (error) => {\n        toast({\n          title: \"Logout failed\",\n          description: error.message,\n          variant: \"destructive\",\n        });\n      }\n    });\n  };\n\n  return (\n    <aside\n      className={className}\n      style={{\n        display: 'flex',\n        flexDirection: 'column',\n        width: '256px',\n        backgroundColor: 'white',\n        borderRight: '1px solid #e5e7eb',\n        height: '100vh'\n      }}\n    >\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        height: '64px',\n        padding: '16px',\n        borderBottom: '1px solid #e5e7eb',\n        backgroundColor: '#f9fafb'\n      }}>\n        <div style={{ display: 'flex', alignItems: 'center' }}>\n          <img\n            src={logoImage}\n            alt=\"LamaMind Logo\"\n            style={{ height: '40px', width: '40px', marginRight: '8px' }}\n          />\n          <span style={{ fontSize: '18px', fontWeight: '600', color: '#111827' }}>\n            LamaMind\n          </span>\n        </div>\n      </div>\n      <nav style={{ flex: 1, padding: '16px 12px' }}>\n        {navItems.map((item) => (\n          <Link\n            key={item.path}\n            href={item.path}\n            onClick={handleClick}\n            style={{\n              display: 'block',\n              marginBottom: '4px',\n              textDecoration: 'none'\n            }}\n          >\n            <div\n              style={{\n                display: 'flex',\n                alignItems: 'center',\n                padding: '12px 16px',\n                fontSize: '14px',\n                fontWeight: '500',\n                borderRadius: '8px',\n                backgroundColor: isLinkActive(item.path) ? '#2563eb' : 'transparent',\n                color: isLinkActive(item.path) ? 'white' : '#374151',\n                cursor: 'pointer',\n                transition: 'all 0.15s ease',\n                border: 'none',\n                width: '100%',\n                boxSizing: 'border-box'\n              }}\n              onMouseEnter={(e) => {\n                if (!isLinkActive(item.path)) {\n                  e.currentTarget.style.backgroundColor = '#f3f4f6';\n                }\n              }}\n              onMouseLeave={(e) => {\n                if (!isLinkActive(item.path)) {\n                  e.currentTarget.style.backgroundColor = 'transparent';\n                }\n              }}\n            >\n              <span\n                className=\"material-icons\"\n                style={{\n                  marginRight: '12px',\n                  fontSize: '18px',\n                  color: isLinkActive(item.path) ? 'white' : '#6b7280'\n                }}\n              >\n                {item.icon}\n              </span>\n              <span>{item.label}</span>\n            </div>\n          </Link>\n        ))}\n      </nav>\n\n      {user && (\n        <div className=\"p-4 border-t border-gray-200 bg-gray-50\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white\">\n                <span className=\"text-sm font-medium\">\n                  {user.name.split(\" \").map(n => n[0]).join(\"\").toUpperCase()}\n                </span>\n              </div>\n            </div>\n            <div className=\"ml-3\">\n              <p className=\"text-sm font-medium text-gray-900\">{user.name}</p>\n              <p className=\"text-xs text-gray-500 capitalize\">{user.userRole}</p>\n            </div>\n            <div className=\"ml-auto flex\">\n              <Link href=\"/profile\">\n                <a\n                  className=\"text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full p-1 mr-1 block\"\n                  title=\"Profile\"\n                >\n                  <span className=\"material-icons text-sm\">person</span>\n                </a>\n              </Link>\n              <button\n                onClick={handleLogout}\n                className=\"text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full p-1\"\n                title=\"Logout\"\n              >\n                <span className=\"material-icons text-sm\">logout</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </aside>\n  );\n}\n", "modifiedCode": "import { Link, useLocation } from \"wouter\";\nimport { cn } from \"@/lib/utils\";\nimport { User } from \"@shared/schema\";\nimport { useAuth } from \"@/hooks/use-auth\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport logoImage from \"../assets/lamaMind-logo.png\";\n\ninterface SidebarProps {\n  user?: User;\n  className?: string;\n  onLinkClick?: () => void;\n}\n\nexport default function Sidebar({ user, className, onLinkClick }: SidebarProps) {\n  const [location, setLocation] = useLocation();\n  const { logoutMutation } = useAuth();\n  const { toast } = useToast();\n\n  const isLinkActive = (path: string) => {\n    if (path === \"/\" && location === \"/\") return true;\n    if (path !== \"/\" && location.startsWith(path)) return true;\n    return false;\n  };\n\n  // Determine which navigation items to show based on user role\n  const baseNavItems = [\n    { path: \"/dashboard\", icon: \"dashboard\", label: \"Dashboard\" },\n    { path: \"/settings\", icon: \"settings\", label: \"Settings\" },\n  ];\n  \n  // Add role-specific navigation items\n  let navItems = [];\n  \n  if (user?.userRole === 'client') {\n    navItems = [\n      ...baseNavItems,\n      { path: \"/voice-therapy\", icon: \"mic\", label: \"Voice Therapy\" },\n    ];\n  } else if (user?.userRole === 'admin') {\n    navItems = [\n      ...baseNavItems,\n      { path: \"/clients\", icon: \"people\", label: \"Clients\" },\n      { path: \"/notes\", icon: \"description\", label: \"Session Notes\" },\n      { path: \"/analytics\", icon: \"insights\", label: \"Analytics\" },\n      { path: \"/admin-dashboard\", icon: \"admin_panel_settings\", label: \"Admin Dashboard\" },\n      { path: \"/admin-ai-test\", icon: \"psychology\", label: \"AI Test Center\" },\n    ];\n  } else {\n    // Default for doctor role\n    navItems = [\n      ...baseNavItems,\n      { path: \"/clients\", icon: \"people\", label: \"Clients\" },\n      { path: \"/notes\", icon: \"description\", label: \"Session Notes\" },\n      { path: \"/analytics\", icon: \"insights\", label: \"Analytics\" },\n    ];\n  }\n\n  const handleClick = () => {\n    if (onLinkClick) onLinkClick();\n  };\n  \n  const handleLogout = () => {\n    logoutMutation.mutate(undefined, {\n      onSuccess: () => {\n        toast({\n          title: \"Logged out successfully\",\n          description: \"You have been logged out of your account.\",\n        });\n        setLocation(\"/auth\");\n      },\n      onError: (error) => {\n        toast({\n          title: \"Logout failed\",\n          description: error.message,\n          variant: \"destructive\",\n        });\n      }\n    });\n  };\n\n  return (\n    <aside\n      className={className}\n      style={{\n        display: 'flex',\n        flexDirection: 'column',\n        width: '256px',\n        backgroundColor: 'white',\n        borderRight: '1px solid #e5e7eb',\n        height: '100vh'\n      }}\n    >\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        height: '64px',\n        padding: '16px',\n        borderBottom: '1px solid #e5e7eb',\n        backgroundColor: '#f9fafb'\n      }}>\n        <div style={{ display: 'flex', alignItems: 'center' }}>\n          <img\n            src={logoImage}\n            alt=\"LamaMind Logo\"\n            style={{ height: '40px', width: '40px', marginRight: '8px' }}\n          />\n          <span style={{ fontSize: '18px', fontWeight: '600', color: '#111827' }}>\n            LamaMind\n          </span>\n        </div>\n      </div>\n      <nav style={{ flex: 1, padding: '16px 12px' }}>\n        {navItems.map((item) => (\n          <Link\n            key={item.path}\n            href={item.path}\n            onClick={handleClick}\n            style={{\n              display: 'block',\n              marginBottom: '4px',\n              textDecoration: 'none'\n            }}\n          >\n            <div\n              style={{\n                display: 'flex',\n                alignItems: 'center',\n                padding: '12px 16px',\n                fontSize: '14px',\n                fontWeight: '500',\n                borderRadius: '8px',\n                backgroundColor: isLinkActive(item.path) ? '#2563eb' : 'transparent',\n                color: isLinkActive(item.path) ? 'white' : '#374151',\n                cursor: 'pointer',\n                transition: 'all 0.15s ease',\n                border: 'none',\n                width: '100%',\n                boxSizing: 'border-box'\n              }}\n              onMouseEnter={(e) => {\n                if (!isLinkActive(item.path)) {\n                  e.currentTarget.style.backgroundColor = '#f3f4f6';\n                }\n              }}\n              onMouseLeave={(e) => {\n                if (!isLinkActive(item.path)) {\n                  e.currentTarget.style.backgroundColor = 'transparent';\n                }\n              }}\n            >\n              <span\n                className=\"material-icons\"\n                style={{\n                  marginRight: '12px',\n                  fontSize: '18px',\n                  color: isLinkActive(item.path) ? 'white' : '#6b7280'\n                }}\n              >\n                {item.icon}\n              </span>\n              <span>{item.label}</span>\n            </div>\n          </Link>\n        ))}\n      </nav>\n\n      {user && (\n        <div className=\"p-4 border-t border-gray-200 bg-gray-50\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white\">\n                <span className=\"text-sm font-medium\">\n                  {user.name.split(\" \").map(n => n[0]).join(\"\").toUpperCase()}\n                </span>\n              </div>\n            </div>\n            <div className=\"ml-3\">\n              <p className=\"text-sm font-medium text-gray-900\">{user.name}</p>\n              <p className=\"text-xs text-gray-500 capitalize\">{user.userRole}</p>\n            </div>\n            <div className=\"ml-auto flex\">\n              <Link href=\"/profile\">\n                <a\n                  className=\"text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full p-1 mr-1 block\"\n                  title=\"Profile\"\n                >\n                  <span className=\"material-icons text-sm\">person</span>\n                </a>\n              </Link>\n              <button\n                onClick={handleLogout}\n                className=\"text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full p-1\"\n                title=\"Logout\"\n              >\n                <span className=\"material-icons text-sm\">logout</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </aside>\n  );\n}\n"}