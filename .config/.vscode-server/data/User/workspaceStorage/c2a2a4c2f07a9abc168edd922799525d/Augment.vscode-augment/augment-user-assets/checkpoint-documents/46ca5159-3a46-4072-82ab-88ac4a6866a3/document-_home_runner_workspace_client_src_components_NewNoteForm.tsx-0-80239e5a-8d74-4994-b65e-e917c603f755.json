{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/NewNoteForm.tsx"}, "originalCode": "import { useForm } from \"react-hook-form\";\nimport { zod<PERSON>esolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\nimport { apiRequest } from \"@/lib/queryClient\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Label } from \"@/components/ui/label\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { useState } from \"react\";\nimport { useToast } from \"@/hooks/use-toast\";\n\ninterface NewNoteFormProps {\n  onSuccess?: () => void;\n}\n\nconst formSchema = z.object({\n  clientId: z.string().min(1, \"Please select a client\"),\n  sessionDate: z.string().min(1, \"Please select a date\"),\n  duration: z.number().min(1, \"Duration must be at least 1 minute\"),\n  content: z.string().min(1, \"Please enter session notes\")\n});\n\ntype FormValues = z.infer<typeof formSchema>;\n\nexport default function NewNoteForm({ onSuccess }: NewNoteFormProps) {\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const { toast } = useToast();\n  const queryClient = useQueryClient();\n\n  // Fetch clients for the select dropdown\n  const { data: clients, isLoading: isLoadingClients } = useQuery({\n    queryKey: [\"/api/clients\"],\n  });\n\n  // Get today's date in YYYY-MM-DD format for the date input\n  const today = new Date().toISOString().split(\"T\")[0];\n\n  const { register, handleSubmit, formState: { errors }, reset } = useForm<FormValues>({\n    resolver: zodResolver(formSchema),\n    defaultValues: {\n      sessionDate: today,\n      duration: 50,\n      content: \"\"\n    }\n  });\n\n  const createNoteMutation = useMutation({\n    mutationFn: async (data: FormValues) => {\n      const res = await apiRequest(\"POST\", \"/api/notes\", {\n        clientId: parseInt(data.clientId),\n        sessionDate: data.sessionDate,\n        duration: data.duration,\n        content: data.content\n      });\n      return await res.json();\n    },\n    onSuccess: () => {\n      // Invalidate related queries to refresh data\n      queryClient.invalidateQueries({ queryKey: [\"/api/notes\"] });\n      queryClient.invalidateQueries({ queryKey: [\"/api/dashboard/stats\"] });\n      \n      // Show success message\n      toast({\n        title: \"Note created successfully\",\n        description: \"Your session note has been submitted for AI analysis\",\n      });\n      \n      // Reset form\n      reset();\n      \n      // Call onSuccess callback if provided\n      if (onSuccess) onSuccess();\n      \n      setIsSubmitting(false);\n    },\n    onError: (error) => {\n      console.error(\"Error creating note:\", error);\n      toast({\n        title: \"Error creating note\",\n        description: \"There was a problem submitting your session note. Please try again.\",\n        variant: \"destructive\"\n      });\n      setIsSubmitting(false);\n    }\n  });\n\n  const onSubmit = (data: FormValues) => {\n    setIsSubmitting(true);\n    createNoteMutation.mutate(data);\n  };\n\n  return (\n    <Card>\n      <CardContent className=\"pt-6\">\n        <h3 className=\"text-lg font-medium text-neutral-dark mb-4\">Add New Session Note</h3>\n        \n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n          <div>\n            <Label htmlFor=\"client-select\">Select Client</Label>\n            <Select disabled={isLoadingClients} {...register(\"clientId\")}>\n              <SelectTrigger className=\"w-full\">\n                <SelectValue placeholder=\"Select a client\" />\n              </SelectTrigger>\n              <SelectContent>\n                {(clients as any[])?.map((client: any) => (\n                  <SelectItem key={client.id} value={client.id.toString()}>\n                    {client.name}\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n            {errors.clientId && (\n              <p className=\"text-sm text-red-500 mt-1\">{errors.clientId.message}</p>\n            )}\n          </div>\n          \n          <div>\n            <Label htmlFor=\"session-date\">Session Date</Label>\n            <Input \n              type=\"date\" \n              id=\"session-date\"\n              max={today}\n              {...register(\"sessionDate\")} \n            />\n            {errors.sessionDate && (\n              <p className=\"text-sm text-red-500 mt-1\">{errors.sessionDate.message}</p>\n            )}\n          </div>\n          \n          <div>\n            <Label htmlFor=\"session-duration\">Session Duration (minutes)</Label>\n            <Input \n              type=\"number\" \n              id=\"session-duration\" \n              min=\"1\" \n              step=\"1\"\n              {...register(\"duration\", { valueAsNumber: true })}\n            />\n            {errors.duration && (\n              <p className=\"text-sm text-red-500 mt-1\">{errors.duration.message}</p>\n            )}\n          </div>\n          \n          <div>\n            <Label htmlFor=\"session-notes\">Session Notes</Label>\n            <Textarea \n              id=\"session-notes\" \n              rows={6} \n              placeholder=\"Enter your detailed session notes here...\"\n              {...register(\"content\")}\n            />\n            <p className=\"mt-1 text-sm text-gray-500\">Your notes will be securely processed and analyzed by our HIPAA-compliant AI.</p>\n            {errors.content && (\n              <p className=\"text-sm text-red-500 mt-1\">{errors.content.message}</p>\n            )}\n          </div>\n          \n          <div className=\"flex justify-end space-x-3\">\n            <Button \n              type=\"button\" \n              variant=\"outline\"\n              onClick={() => reset()}\n              disabled={isSubmitting}\n            >\n              Clear\n            </Button>\n            <Button \n              type=\"submit\"\n              disabled={isSubmitting}\n            >\n              {isSubmitting ? (\n                <>\n                  <span className=\"material-icons text-sm mr-2 animate-spin\">progress_activity</span>\n                  Processing...\n                </>\n              ) : (\n                <>\n                  <span className=\"material-icons text-sm mr-2\">psychology</span>\n                  Analyze Session\n                </>\n              )}\n            </Button>\n          </div>\n        </form>\n      </CardContent>\n    </Card>\n  );\n}\n", "modifiedCode": "import { useForm } from \"react-hook-form\";\nimport { zod<PERSON>esolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\nimport { apiRequest } from \"@/lib/queryClient\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Label } from \"@/components/ui/label\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { useState } from \"react\";\nimport { useToast } from \"@/hooks/use-toast\";\n\ninterface NewNoteFormProps {\n  onSuccess?: () => void;\n}\n\nconst formSchema = z.object({\n  clientId: z.string().min(1, \"Please select a client\"),\n  sessionDate: z.string().min(1, \"Please select a date\"),\n  duration: z.number().min(1, \"Duration must be at least 1 minute\"),\n  content: z.string().min(1, \"Please enter session notes\")\n});\n\ntype FormValues = z.infer<typeof formSchema>;\n\nexport default function NewNoteForm({ onSuccess }: NewNoteFormProps) {\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const { toast } = useToast();\n  const queryClient = useQueryClient();\n\n  // Fetch clients for the select dropdown\n  const { data: clients, isLoading: isLoadingClients } = useQuery({\n    queryKey: [\"/api/clients\"],\n  });\n\n  // Get today's date in YYYY-MM-DD format for the date input\n  const today = new Date().toISOString().split(\"T\")[0];\n\n  const { register, handleSubmit, formState: { errors }, reset } = useForm<FormValues>({\n    resolver: zodResolver(formSchema),\n    defaultValues: {\n      sessionDate: today,\n      duration: 50,\n      content: \"\"\n    }\n  });\n\n  const createNoteMutation = useMutation({\n    mutationFn: async (data: FormValues) => {\n      const res = await apiRequest(\"POST\", \"/api/notes\", {\n        clientId: parseInt(data.clientId),\n        sessionDate: data.sessionDate,\n        duration: data.duration,\n        content: data.content\n      });\n      return await res.json();\n    },\n    onSuccess: () => {\n      // Invalidate related queries to refresh data\n      queryClient.invalidateQueries({ queryKey: [\"/api/notes\"] });\n      queryClient.invalidateQueries({ queryKey: [\"/api/dashboard/stats\"] });\n      \n      // Show success message\n      toast({\n        title: \"Note created successfully\",\n        description: \"Your session note has been submitted for AI analysis\",\n      });\n      \n      // Reset form\n      reset();\n      \n      // Call onSuccess callback if provided\n      if (onSuccess) onSuccess();\n      \n      setIsSubmitting(false);\n    },\n    onError: (error) => {\n      console.error(\"Error creating note:\", error);\n      toast({\n        title: \"Error creating note\",\n        description: \"There was a problem submitting your session note. Please try again.\",\n        variant: \"destructive\"\n      });\n      setIsSubmitting(false);\n    }\n  });\n\n  const onSubmit = (data: FormValues) => {\n    setIsSubmitting(true);\n    createNoteMutation.mutate(data);\n  };\n\n  return (\n    <Card>\n      <CardContent className=\"pt-6\">\n        <h3 className=\"text-lg font-medium text-neutral-dark mb-4\">Add New Session Note</h3>\n        \n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n          <div>\n            <Label htmlFor=\"client-select\">Select Client</Label>\n            <Select disabled={isLoadingClients} {...register(\"clientId\")}>\n              <SelectTrigger className=\"w-full\">\n                <SelectValue placeholder=\"Select a client\" />\n              </SelectTrigger>\n              <SelectContent>\n                {(clients as any[])?.map((client: any) => (\n                  <SelectItem key={client.id} value={client.id.toString()}>\n                    {client.name}\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n            {errors.clientId && (\n              <p className=\"text-sm text-red-500 mt-1\">{errors.clientId.message}</p>\n            )}\n          </div>\n          \n          <div>\n            <Label htmlFor=\"session-date\">Session Date</Label>\n            <Input \n              type=\"date\" \n              id=\"session-date\"\n              max={today}\n              {...register(\"sessionDate\")} \n            />\n            {errors.sessionDate && (\n              <p className=\"text-sm text-red-500 mt-1\">{errors.sessionDate.message}</p>\n            )}\n          </div>\n          \n          <div>\n            <Label htmlFor=\"session-duration\">Session Duration (minutes)</Label>\n            <Input \n              type=\"number\" \n              id=\"session-duration\" \n              min=\"1\" \n              step=\"1\"\n              {...register(\"duration\", { valueAsNumber: true })}\n            />\n            {errors.duration && (\n              <p className=\"text-sm text-red-500 mt-1\">{errors.duration.message}</p>\n            )}\n          </div>\n          \n          <div>\n            <Label htmlFor=\"session-notes\">Session Notes</Label>\n            <Textarea \n              id=\"session-notes\" \n              rows={6} \n              placeholder=\"Enter your detailed session notes here...\"\n              {...register(\"content\")}\n            />\n            <p className=\"mt-1 text-sm text-gray-500\">Your notes will be securely processed and analyzed by our HIPAA-compliant AI.</p>\n            {errors.content && (\n              <p className=\"text-sm text-red-500 mt-1\">{errors.content.message}</p>\n            )}\n          </div>\n          \n          <div className=\"flex justify-end space-x-3\">\n            <Button \n              type=\"button\" \n              variant=\"outline\"\n              onClick={() => reset()}\n              disabled={isSubmitting}\n            >\n              Clear\n            </Button>\n            <Button \n              type=\"submit\"\n              disabled={isSubmitting}\n            >\n              {isSubmitting ? (\n                <>\n                  <span className=\"material-icons text-sm mr-2 animate-spin\">progress_activity</span>\n                  Processing...\n                </>\n              ) : (\n                <>\n                  <span className=\"material-icons text-sm mr-2\">psychology</span>\n                  Analyze Session\n                </>\n              )}\n            </Button>\n          </div>\n        </form>\n      </CardContent>\n    </Card>\n  );\n}\n"}