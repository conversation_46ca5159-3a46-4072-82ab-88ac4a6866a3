{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/MobileNav.tsx"}, "originalCode": "import { Link, useLocation } from \"wouter\";\nimport { cn } from \"@/lib/utils\";\n\nexport default function MobileNav() {\n  const [location] = useLocation();\n\n  const isLinkActive = (path: string) => {\n    if (path === \"/\" && location === \"/\") return true;\n    if (path !== \"/\" && location.startsWith(path)) return true;\n    return false;\n  };\n\n  const navItems = [\n    { path: \"/\", icon: \"dashboard\", label: \"Dashboard\" },\n    { path: \"/clients\", icon: \"people\", label: \"Clients\" },\n    { path: \"/notes\", icon: \"description\", label: \"Notes\" },\n    { path: \"/settings\", icon: \"more_horiz\", label: \"More\" },\n  ];\n\n  return (\n    <div className=\"block md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-10\">\n      <div className=\"flex justify-around\">\n        {navItems.map((item) => (\n          <Link key={item.path} href={item.path}>\n            <a\n              className={cn(\n                \"flex flex-col items-center py-2\",\n                isLinkActive(item.path) ? \"text-primary\" : \"text-neutral-dark hover:text-primary\"\n              )}\n            >\n              <span className=\"material-icons\">{item.icon}</span>\n              <span className=\"text-xs\">{item.label}</span>\n            </a>\n          </Link>\n        ))}\n      </div>\n    </div>\n  );\n}\n", "modifiedCode": "import { Link, useLocation } from \"wouter\";\nimport { cn } from \"@/lib/utils\";\n\nexport default function MobileNav() {\n  const [location] = useLocation();\n\n  const isLinkActive = (path: string) => {\n    if (path === \"/\" && location === \"/\") return true;\n    if (path !== \"/\" && location.startsWith(path)) return true;\n    return false;\n  };\n\n  const navItems = [\n    { path: \"/\", icon: \"dashboard\", label: \"Dashboard\" },\n    { path: \"/clients\", icon: \"people\", label: \"Clients\" },\n    { path: \"/notes\", icon: \"description\", label: \"Notes\" },\n    { path: \"/settings\", icon: \"more_horiz\", label: \"More\" },\n  ];\n\n  return (\n    <div className=\"block md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-10\">\n      <div className=\"flex justify-around\">\n        {navItems.map((item) => (\n          <Link key={item.path} href={item.path}>\n            <div\n              className={cn(\n                \"flex flex-col items-center py-2 cursor-pointer\",\n                isLinkActive(item.path) ? \"text-primary\" : \"text-neutral-dark hover:text-primary\"\n              )}\n            >\n              <span className=\"material-icons\">{item.icon}</span>\n              <span className=\"text-xs\">{item.label}</span>\n            </div>\n          </Link>\n        ))}\n      </div>\n    </div>\n  );\n}\n"}