{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/vite.ts"}, "originalCode": "import express, { type Express } from \"express\";\nimport fs from \"fs\";\nimport path from \"path\";\nimport { createServer as createViteServer, createLogger } from \"vite\";\nimport { type Server } from \"http\";\nimport viteConfig from \"../vite.config\";\nimport { nanoid } from \"nanoid\";\n\nconst viteLogger = createLogger();\n\nexport function log(message: string, source = \"express\") {\n  const formattedTime = new Date().toLocaleTimeString(\"en-US\", {\n    hour: \"numeric\",\n    minute: \"2-digit\",\n    second: \"2-digit\",\n    hour12: true,\n  });\n\n  console.log(`${formattedTime} [${source}] ${message}`);\n}\n\nexport async function setupVite(app: Express, server: Server) {\n  const serverOptions = {\n    middlewareMode: true,\n    hmr: { \n      server,\n      path: '/__vite_hmr'  // Explicit path to avoid conflicts with /ws\n    },\n    allowedHosts: true as const,  // Allow all hosts in development (Replit environment)\n  };\n\n  const vite = await createViteServer({\n    ...viteConfig,\n    configFile: false,\n    customLogger: {\n      ...viteLogger,\n      error: (msg, options) => {\n        viteLogger.error(msg, options);\n        process.exit(1);\n      },\n    },\n    server: serverOptions,\n    appType: \"custom\",\n  });\n\n  app.use(vite.middlewares);\n  app.use(\"*\", async (req, res, next) => {\n    const url = req.originalUrl;\n\n    try {\n      const clientTemplate = path.resolve(\n        import.meta.dirname,\n        \"..\",\n        \"client\",\n        \"index.html\",\n      );\n\n      // always reload the index.html file from disk incase it changes\n      let template = await fs.promises.readFile(clientTemplate, \"utf-8\");\n      template = template.replace(\n        `src=\"/src/main.tsx\"`,\n        `src=\"/src/main.tsx?v=${nanoid()}\"`,\n      );\n      const page = await vite.transformIndexHtml(url, template);\n      res.status(200).set({ \"Content-Type\": \"text/html\" }).end(page);\n    } catch (e) {\n      vite.ssrFixStacktrace(e as Error);\n      next(e);\n    }\n  });\n}\n\nexport function serveStatic(app: Express) {\n  const distPath = path.resolve(import.meta.dirname, \"public\");\n\n  if (!fs.existsSync(distPath)) {\n    throw new Error(\n      `Could not find the build directory: ${distPath}, make sure to build the client first`,\n    );\n  }\n\n  app.use(express.static(distPath));\n\n  // fall through to index.html if the file doesn't exist\n  app.use(\"*\", (_req, res) => {\n    res.sendFile(path.resolve(distPath, \"index.html\"));\n  });\n}\n", "modifiedCode": "import express, { type Express } from \"express\";\nimport fs from \"fs\";\nimport path from \"path\";\nimport { createServer as createViteServer, createLogger } from \"vite\";\nimport { type Server } from \"http\";\nimport viteConfig from \"../vite.config\";\nimport { nanoid } from \"nanoid\";\n\nconst viteLogger = createLogger();\n\nexport function log(message: string, source = \"express\") {\n  const formattedTime = new Date().toLocaleTimeString(\"en-US\", {\n    hour: \"numeric\",\n    minute: \"2-digit\",\n    second: \"2-digit\",\n    hour12: true,\n  });\n\n  console.log(`${formattedTime} [${source}] ${message}`);\n}\n\nexport async function setupVite(app: Express, server: Server) {\n  const serverOptions = {\n    middlewareMode: true,\n    hmr: { \n      server,\n      path: '/__vite_hmr'  // Explicit path to avoid conflicts with /ws\n    },\n    allowedHosts: true as const,  // Allow all hosts in development (Replit environment)\n  };\n\n  const vite = await createViteServer({\n    ...viteConfig,\n    configFile: false,\n    customLogger: {\n      ...viteLogger,\n      error: (msg, options) => {\n        viteLogger.error(msg, options);\n        process.exit(1);\n      },\n    },\n    server: serverOptions,\n    appType: \"custom\",\n  });\n\n  app.use(vite.middlewares);\n  app.use(\"*\", async (req, res, next) => {\n    const url = req.originalUrl;\n\n    try {\n      const clientTemplate = path.resolve(\n        import.meta.dirname,\n        \"..\",\n        \"client\",\n        \"index.html\",\n      );\n\n      // always reload the index.html file from disk incase it changes\n      let template = await fs.promises.readFile(clientTemplate, \"utf-8\");\n      template = template.replace(\n        `src=\"/src/main.tsx\"`,\n        `src=\"/src/main.tsx?v=${nanoid()}\"`,\n      );\n      const page = await vite.transformIndexHtml(url, template);\n      res.status(200).set({ \"Content-Type\": \"text/html\" }).end(page);\n    } catch (e) {\n      vite.ssrFixStacktrace(e as Error);\n      next(e);\n    }\n  });\n}\n\nexport function serveStatic(app: Express) {\n  const distPath = path.resolve(process.cwd(), \"dist\", \"public\");\n\n  if (!fs.existsSync(distPath)) {\n    throw new Error(\n      `Could not find the build directory: ${distPath}, make sure to build the client first`,\n    );\n  }\n\n  app.use(express.static(distPath));\n\n  // fall through to index.html if the file doesn't exist\n  app.use(\"*\", (_req, res) => {\n    res.sendFile(path.resolve(distPath, \"index.html\"));\n  });\n}\n"}