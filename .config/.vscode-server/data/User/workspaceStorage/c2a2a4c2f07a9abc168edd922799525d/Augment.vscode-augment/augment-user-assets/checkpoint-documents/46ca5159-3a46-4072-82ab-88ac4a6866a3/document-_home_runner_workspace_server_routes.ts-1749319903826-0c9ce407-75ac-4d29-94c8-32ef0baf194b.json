{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes.ts"}, "originalCode": "import { Express, Request, Response, NextFunction } from 'express';\nimport { Server as HttpServer, createServer } from 'http';\nimport { WebSocket } from 'ws';\nimport multer from 'multer';\nimport rateLimit from 'express-rate-limit';\nimport { body, validationResult } from 'express-validator';\nimport csrf from 'csrf';\nimport path from 'path';\nimport fs from 'fs';\nimport { v4 as uuidv4 } from 'uuid';\nimport OpenAI from 'openai';\nimport {\n  analyzeTherapyNotes,\n  transcribeAudio,\n  streamAITherapistResponse\n} from './openai';\nimport { storage } from './storage';\nimport {\n  insertSessionNoteSchema,\n  insertClientThemeSchema,\n  insertAiTherapySettingsSchema,\n  insertInvitationCodeSchema\n} from '@shared/schema';\nimport { getDashboardStatsSQL, getRecentSessionNotesSQL } from './custom-queries';\nimport { pool } from './db';\nimport { setupAuth } from './auth';\nimport { directLogin<PERSON><PERSON><PERSON>, createAdminUser } from './temp-login-fix';\nimport {\n  Client,\n  ConversationMessage\n} from './types';\n// WebSocket handler is imported in index.ts\n\n// Set up multer for file upload handling\nconst uploadsDir = path.join(process.cwd(), 'uploads');\n\n// Create uploads directory if it doesn't exist\nif (!fs.existsSync(uploadsDir)) {\n  fs.mkdirSync(uploadsDir, { recursive: true });\n}\n\nconst upload = multer({\n  storage: multer.diskStorage({\n    destination: (_req, _file, cb) => {\n      cb(null, uploadsDir);\n    },\n    filename: (_req, file, cb) => {\n      // Create unique filename with timestamp and original extension\n      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);\n      cb(null, uniqueSuffix + path.extname(file.originalname));\n    }\n  }),\n  limits: {\n    fileSize: 50 * 1024 * 1024, // 50 MB max file size\n  }\n});\n\n// Temporary in-memory store for voice therapy sessions\n// Use this when database connection fails\n// Note: These are currently unused but kept for potential fallback functionality\n\ndeclare global {\n  namespace Express {\n    interface User {\n      id: number;\n      username: string;\n      [key: string]: any;\n    }\n  }\n}\n\n// Add custom properties to express-session\ndeclare module 'express-session' {\n  interface SessionData {\n    csrfSecret: string;\n  }\n}\n\nfunction randomInvitationCode(length = 8): string {\n  const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += characters.charAt(Math.floor(Math.random() * characters.length));\n  }\n  return result;\n}\n\nexport async function registerRoutes(app: Express): Promise<HttpServer> {\n  // Create admin user if needed\n  try {\n    await createAdminUser();\n    console.log('[STARTUP] Admin user check complete');\n  } catch (error) {\n    console.error('[STARTUP] Error checking/creating admin user:', error);\n  }\n\n  // SECURITY: Add rate limiting\n  const authLimiter = rateLimit({\n    windowMs: 15 * 60 * 1000, // 15 minutes\n    max: 5, // Limit each IP to 5 requests per windowMs for auth routes\n    message: 'Too many authentication attempts, please try again later.',\n    standardHeaders: true,\n    legacyHeaders: false,\n  });\n\n  const generalLimiter = rateLimit({\n    windowMs: 1 * 60 * 1000, // 1 minutes\n    max: process.env.NODE_ENV === 'development' ? 1000 : 1000, // possiblyHigher limit for development\n    message: 'Too many requests, please try again later.',\n    standardHeaders: true,\n    legacyHeaders: false,\n  });\n\n  // Apply general rate limiting to all routes\n  app.use(generalLimiter);\n\n  // CSRF Protection\n  const tokens = new csrf();\n  app.use((req, _res, next) => {\n    if (!req.session.csrfSecret) {\n      req.session.csrfSecret = tokens.secretSync();\n    }\n    next();\n  });\n\n  // CSRF token endpoint\n  app.get('/api/csrf-token', (req, res) => {\n    const csrfSecret = req.session.csrfSecret;\n    if (!csrfSecret) {\n      return res.status(500).json({ message: 'CSRF secret not initialized' });\n    }\n    const token = tokens.create(csrfSecret);\n    res.json({ csrfToken: token });\n  });\n\n  // CSRF validation middleware (currently unused but kept for potential use)\n  const _validateCSRF = (req: Request, res: Response, next: NextFunction) => {\n    if (req.method === 'GET' || req.method === 'HEAD' || req.method === 'OPTIONS') {\n      return next();\n    }\n\n    const token = req.headers['x-csrf-token'] || req.body._csrf;\n    const csrfSecret = req.session.csrfSecret;\n    if (!token || !csrfSecret || !tokens.verify(csrfSecret, token.toString())) {\n      return res.status(403).json({ message: 'Invalid CSRF token' });\n    }\n    next();\n  };\n\n  // SECURITY: Add security headers\n  app.use((req, res, next) => {\n    // Prevent clickjacking\n    res.setHeader('X-Frame-Options', 'DENY');\n    // Prevent MIME type sniffing\n    res.setHeader('X-Content-Type-Options', 'nosniff');\n    // Enable XSS protection\n    res.setHeader('X-XSS-Protection', '1; mode=block');\n    // Referrer policy\n    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');\n    // Content Security Policy\n    res.setHeader('Content-Security-Policy', \"default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:;\");\n    // Strict Transport Security (for HTTPS)\n    if (req.secure) {\n      res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');\n    }\n    next();\n  });\n\n  // Set up standard auth routes (/api/register, /api/login, /api/logout, /api/user)\n  setupAuth(app);\n\n  // Apply auth rate limiting to authentication routes\n  app.use('/api/login', authLimiter);\n  app.use('/api/register', authLimiter);\n  app.use('/api/direct-login', authLimiter);\n\n  // Middleware to handle validation errors\n  const handleValidationErrors = (req: Request, res: Response, next: NextFunction) => {\n    const errors = validationResult(req);\n    if (!errors.isEmpty()) {\n      console.log(`[SECURITY] Validation failed for ${req.method} ${req.path}:`, errors.array());\n      return res.status(400).json({\n        message: 'Validation failed',\n        errors: errors.array()\n      });\n    }\n    next();\n  };\n\n  // Input validation for authentication\n  const authValidation = [\n    body('username').isLength({ min: 1 }).trim().escape().withMessage('Username is required'),\n    body('password').isLength({ min: 1 }).withMessage('Password is required'),\n    handleValidationErrors\n  ];\n\n  // Add direct login route that bypasses Passport.js\n  app.post('/api/direct-login', authValidation, directLoginHandler);\n  console.log('[STARTUP] Direct login route registered at /api/direct-login');\n  \n  const isAuthenticated = (req: Request, res: Response, next: NextFunction) => {\n    if (req.isAuthenticated()) {\n      return next();\n    }\n    res.status(401).json({ message: 'Not authenticated' });\n  };\n\n  // Security audit logging (currently unused but kept for potential use)\n  const _auditLog = (action: string, userId?: number, details?: any) => {\n    const timestamp = new Date().toISOString();\n    console.log(`[AUDIT] ${timestamp} - ${action} - User: ${userId || 'anonymous'} - Details:`, details);\n  };\n  \n  const isAdmin = (req: Request, res: Response, next: NextFunction) => {\n    if (req.isAuthenticated() && req.user && req.user.userRole === 'admin') {\n      return next();\n    }\n    res.status(403).json({ message: 'Admin access required' });\n  };\n  \n  const handleError = (res: Response, error: unknown) => {\n    console.error('API Error:', error);\n\n    // Don't leak sensitive error information in production\n    if (process.env.NODE_ENV === 'production') {\n      res.status(500).json({ message: 'Internal server error' });\n    } else {\n      res.status(500).json({ message: error instanceof Error ? error.message : 'Server error' });\n    }\n  };\n  \n  // ------------------------\n  // CLIENTS ROUTES\n  // ------------------------\n  \n  // Get all clients for a doctor\n  app.get('/api/clients', isAuthenticated, async (req, res) => {\n    try {\n      const userId = req.user?.id;\n      \n      if (!userId || isNaN(userId)) {\n        return res.status(401).json({ message: 'User ID is required' });\n      }\n      \n      const user = await storage.getUser(Number(userId));\n      \n      if (!user) {\n        return res.status(404).json({ message: 'User not found' });\n      }\n      \n      let clients: Client[] = [];\n\n      if (user.userRole === 'admin') {\n        // Admin can see all clients\n        clients = await storage.getAllClients();\n      } else if (user.userRole === 'doctor') {\n        // Doctor can only see their assigned clients\n        clients = await storage.getClientsByDoctor(Number(userId));\n      } else if (user.userRole === 'client') {\n        // Client can only see themselves\n        const client = await storage.getClientByUserId(Number(userId));\n        if (client) {\n          clients = [client];\n        }\n      }\n      \n      res.json(clients);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Create a client\n  app.post('/api/clients', isAuthenticated, async (req, res) => {\n    try {\n      if (req.user?.userRole !== 'doctor' && req.user?.userRole !== 'admin') {\n        return res.status(403).json({ message: 'Not authorized to create clients' });\n      }\n      \n      const clientData = req.body;\n      const client = await storage.createClient(clientData);\n      \n      // Assign doctor-client relationship\n      if (req.user?.userRole === 'doctor') {\n        await storage.createDoctorClient({\n          doctorId: req.user.id,\n          clientId: client.userId\n        });\n      }\n      \n      res.status(201).json(client);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Update client status\n  app.patch('/api/clients/:id/status', isAuthenticated, async (req, res) => {\n    try {\n      const clientId = parseInt(req.params.id);\n      const { status } = req.body;\n      \n      if (!status) {\n        return res.status(400).json({ message: 'Status is required' });\n      }\n      \n      const client = await storage.updateClientStatus(Number(clientId), status);\n      \n      if (!client) {\n        return res.status(404).json({ message: 'Client not found' });\n      }\n      \n      res.json(client);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // ------------------------\n  // SESSION NOTES ROUTES\n  // ------------------------\n  \n  // Get all session notes for logged in user (doctor or client)\n  app.get('/api/notes', isAuthenticated, async (req, res) => {\n    try {\n      const userId = req.user?.id;\n      \n      if (!userId || isNaN(userId)) {\n        return res.status(401).json({ message: 'User ID is required' });\n      }\n      \n      // Pagination parameters\n      const page = parseInt(req.query.page as string) || 1;\n      const limit = parseInt(req.query.limit as string) || 10;\n      const clientId = req.query.clientId ? parseInt(req.query.clientId as string) : undefined;\n      \n      // Get notes based on user role\n      let notes = [];\n      \n      if (clientId) {\n        // If a specific client ID is requested\n        notes = await storage.getSessionNotesByClientId(Number(clientId));\n      } else {\n        // Get recent notes for the user using SQL directly\n        notes = await getRecentSessionNotesSQL(pool, Number(userId), limit);\n      }\n      \n      res.json(notes);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Get a specific session note\n  app.get('/api/notes/:id', isAuthenticated, async (req, res) => {\n    try {\n      const noteId = parseInt(req.params.id);\n      const note = await storage.getSessionNote(Number(noteId));\n      \n      if (!note) {\n        return res.status(404).json({ message: 'Note not found' });\n      }\n      \n      // Check if user has access to this note\n      const userId = req.user?.id;\n      const userRole = req.user?.userRole;\n      \n      if (userRole === 'admin') {\n        // Admin can access any note\n      } else if (userRole === 'doctor') {\n        // Doctor can access notes for their clients\n        const clients = await storage.getClientsByUserId(Number(userId));\n        const clientIds = clients.map(c => c.id);\n        \n        if (!clientIds.includes(note.clientId)) {\n          return res.status(403).json({ message: 'Not authorized to access this note' });\n        }\n      } else if (userRole === 'client') {\n        // Client can only access their own notes\n        const client = await storage.getClientByUserId(Number(userId));\n        \n        if (!client || note.clientId !== client.id) {\n          return res.status(403).json({ message: 'Not authorized to access this note' });\n        }\n      }\n      \n      res.json(note);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Input validation for notes\n  const noteValidation = [\n    body('content').isLength({ min: 1, max: 10000 }).trim().withMessage('Content is required and must be less than 10000 characters'),\n    body('clientId').isInt({ min: 1 }).withMessage('Valid client ID is required'),\n    body('sessionDate').isISO8601().withMessage('Valid session date is required'),\n    body('duration').optional().isInt({ min: 1, max: 480 }).withMessage('Duration must be between 1 and 480 minutes'),\n    handleValidationErrors\n  ];\n\n  // Create a new session note\n  app.post('/api/notes', isAuthenticated, noteValidation, async (req, res) => {\n    try {\n      if (req.user?.userRole !== 'doctor' && req.user?.userRole !== 'admin') {\n        return res.status(403).json({ message: 'Not authorized to create notes' });\n      }\n      \n      const noteData = insertSessionNoteSchema.parse({\n        ...req.body,\n        createdBy: req.user.id\n      });\n      \n      const note = await storage.createSessionNote(noteData);\n      \n      // Process with AI in the background\n      analyzeTherapyNotes(note.content)\n        .then(async (analysis) => {\n          // Update the note with AI analysis\n          await storage.updateSessionNoteAnalysis(\n            note.id,\n            analysis.summary,\n            analysis.themes,\n            analysis.recommendations\n          );\n          \n          // Update client themes based on analysis\n          if (analysis.themes) {\n            for (const theme of analysis.themes) {\n              await storage.updateOrCreateClientTheme(\n                note.clientId,\n                theme.name,\n                theme.trend\n              );\n            }\n          }\n        })\n        .catch(err => {\n          console.error('Error analyzing notes:', err);\n        });\n      \n      res.status(201).json(note);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // ------------------------\n  // THEME ROUTES\n  // ------------------------\n  \n  // Get themes for a client\n  app.get('/api/clients/:id/themes', isAuthenticated, async (req, res) => {\n    try {\n      const clientId = parseInt(req.params.id);\n\n      // SECURITY: Check access permissions\n      const userId = req.user!.id;\n      const userRole = req.user!.userRole;\n\n      if (userRole === 'admin') {\n        // Admin can access all client themes\n      } else if (userRole === 'doctor') {\n        // Doctor can only access themes for their assigned clients\n        const doctorClients = await storage.getClientsByDoctor(userId);\n        const hasAccess = doctorClients.some(c => c.id === clientId);\n\n        if (!hasAccess) {\n          return res.status(403).json({ message: 'Access denied: Not your assigned client' });\n        }\n      } else if (userRole === 'client') {\n        // Client can only access their own themes\n        const client = await storage.getClientByUserId(userId);\n        if (!client || client.id !== clientId) {\n          return res.status(403).json({ message: 'Access denied: Not your themes' });\n        }\n      } else {\n        return res.status(403).json({ message: 'Access denied: Invalid role' });\n      }\n\n      const themes = await storage.getClientThemes(Number(clientId));\n      res.json(themes);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Create or update a theme for a client\n  app.post('/api/clients/:id/themes', isAuthenticated, async (req, res) => {\n    try {\n      if (req.user?.userRole !== 'doctor' && req.user?.userRole !== 'admin') {\n        return res.status(403).json({ message: 'Not authorized to manage themes' });\n      }\n      \n      const clientId = parseInt(req.params.id);\n      if (!clientId || isNaN(clientId)) {\n        return res.status(400).json({ message: 'Client ID is required' });\n      }\n      \n      const { name, trend } = insertClientThemeSchema.parse(req.body);\n      const trendNumber = Number(trend);\n      \n      if (isNaN(trendNumber)) {\n        return res.status(400).json({ message: 'Trend must be a number' });\n      }\n      \n      const theme = await storage.updateOrCreateClientTheme(clientId, name, trendNumber);\n      res.status(201).json(theme);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // ------------------------\n  // AI THERAPY ROUTES\n  // ------------------------\n  \n  // Get all AI therapy conversations for a user\n  app.get('/api/therapy/conversations', isAuthenticated, async (req, res) => {\n    try {\n      const userId = req.user?.id;\n      if (!userId || isNaN(userId)) {\n        return res.status(401).json({ message: 'User ID is required' });\n      }\n      \n      const clientIdParam = req.query.clientId ? parseInt(req.query.clientId as string) : undefined;\n      const clientId = clientIdParam && !isNaN(clientIdParam) ? Number(clientIdParam) : undefined;\n      \n      const conversations = await storage.getAiTherapyConversations(Number(userId), clientId);\n      res.json(conversations);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Get a specific AI therapy conversation\n  app.get('/api/therapy/conversations/:id', isAuthenticated, async (req, res) => {\n    try {\n      const conversationId = parseInt(req.params.id);\n      const conversation = await storage.getAiTherapyConversation(Number(conversationId));\n\n      if (!conversation) {\n        return res.status(404).json({ message: 'Conversation not found' });\n      }\n\n      // SECURITY: Check access permissions\n      const userId = req.user!.id;\n      const userRole = req.user!.userRole;\n\n      if (userRole === 'admin') {\n        // Admin can access all conversations\n      } else if (userRole === 'doctor') {\n        // Doctor can only access conversations of their assigned clients\n        const client = await storage.getClient(conversation.clientId);\n        if (!client) {\n          return res.status(404).json({ message: 'Client not found' });\n        }\n\n        const doctorClients = await storage.getClientsByDoctor(userId);\n        const hasAccess = doctorClients.some(c => c.id === conversation.clientId);\n\n        if (!hasAccess) {\n          return res.status(403).json({ message: 'Access denied: Not your assigned client' });\n        }\n      } else {\n        // Client can only access their own conversations\n        if (conversation.clientId !== userId) {\n          return res.status(403).json({ message: 'Access denied: Not your conversation' });\n        }\n      }\n\n      // Get messages for this conversation\n      const messages = await storage.getAiTherapyMessages(Number(conversationId));\n\n      res.json({\n        conversation,\n        messages\n      });\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Create a new AI therapy conversation\n  app.post('/api/therapy/conversations', isAuthenticated, async (req, res) => {\n    try {\n      const conversationData = {\n        ...req.body,\n        userId: req.user?.id\n      };\n      \n      const conversation = await storage.createAiTherapyConversation(conversationData);\n      res.status(201).json(conversation);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // End an AI therapy conversation\n  app.post('/api/therapy/conversations/:id/end', isAuthenticated, async (req, res) => {\n    try {\n      const conversationId = parseInt(req.params.id);\n      const { summary } = req.body;\n\n      if (!summary) {\n        return res.status(400).json({ message: 'Summary is required' });\n      }\n\n      // SECURITY: Check access permissions before ending conversation\n      const existingConversation = await storage.getAiTherapyConversation(Number(conversationId));\n      if (!existingConversation) {\n        return res.status(404).json({ message: 'Conversation not found' });\n      }\n\n      const userId = req.user!.id;\n      const userRole = req.user!.userRole;\n\n      if (userRole === 'admin') {\n        // Admin can end any conversation\n      } else if (userRole === 'doctor') {\n        // Doctor can only end conversations of their assigned clients\n        const doctorClients = await storage.getClientsByDoctor(userId);\n        const hasAccess = doctorClients.some(c => c.id === existingConversation.clientId);\n\n        if (!hasAccess) {\n          return res.status(403).json({ message: 'Access denied: Not your assigned client' });\n        }\n      } else {\n        // Client can only end their own conversations\n        if (existingConversation.clientId !== userId) {\n          return res.status(403).json({ message: 'Access denied: Not your conversation' });\n        }\n      }\n\n      const conversation = await storage.endAiTherapyConversation(Number(conversationId), summary);\n\n      if (!conversation) {\n        return res.status(404).json({ message: 'Conversation not found' });\n      }\n\n      res.json(conversation);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Send a message in an AI therapy conversation\n  app.post('/api/therapy/conversations/:id/messages', isAuthenticated, async (req, res) => {\n    try {\n      const conversationId = parseInt(req.params.id);\n      const { content, role } = req.body;\n\n      if (!content) {\n        return res.status(400).json({ message: 'Content is required' });\n      }\n\n      // SECURITY: Check access permissions before sending message\n      const conversation = await storage.getAiTherapyConversation(Number(conversationId));\n      if (!conversation) {\n        return res.status(404).json({ message: 'Conversation not found' });\n      }\n\n      const userId = req.user!.id;\n      const userRole = req.user!.userRole;\n\n      if (userRole === 'admin') {\n        // Admin can send messages in any conversation\n      } else if (userRole === 'doctor') {\n        // Doctor can only send messages in conversations of their assigned clients\n        const doctorClients = await storage.getClientsByDoctor(userId);\n        const hasAccess = doctorClients.some(c => c.id === conversation.clientId);\n\n        if (!hasAccess) {\n          return res.status(403).json({ message: 'Access denied: Not your assigned client' });\n        }\n      } else {\n        // Client can only send messages in their own conversations\n        if (conversation.clientId !== userId) {\n          return res.status(403).json({ message: 'Access denied: Not your conversation' });\n        }\n      }\n      \n      // Create user message\n      const message = await storage.createAiTherapyMessage({\n        conversationId,\n        content,\n        role: role || 'user',\n        timestamp: new Date()\n      });\n      \n      // Get AI response\n      const rawMessages = await storage.getAiTherapyMessages(conversationId);\n      const conversationHistory: ConversationMessage[] = rawMessages.map(msg => ({\n        role: msg.role as 'user' | 'assistant',\n        content: msg.content,\n        timestamp: msg.timestamp\n      }));\n      \n      const settings = await storage.getActiveAiTherapySettings();\n      \n      // Create a temporary WebSocket for streaming\n      const ws = new WebSocket('ws://localhost:3000/ws');\n      \n      // Wait for connection\n      await new Promise((resolve) => ws.on('open', resolve));\n      \n      // Get AI response\n      const aiResponse = await streamAITherapistResponse(\n        ws,\n        content,\n        conversationHistory,\n        true,\n        settings?.systemPrompt\n      );\n      \n      // Save AI response\n      const aiMessage = await storage.createAiTherapyMessage({\n        conversationId,\n        content: aiResponse.message,\n        role: 'assistant',\n        timestamp: new Date()\n      });\n      \n      res.status(201).json({\n        userMessage: message,\n        aiMessage\n      });\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // ------------------------\n  // AI SETTINGS ROUTES\n  // ------------------------\n  \n  // Get all AI therapy settings\n  app.get('/api/therapy/settings', isAuthenticated, async (req, res) => {\n    try {\n      if (req.user?.userRole !== 'admin') {\n        return res.status(403).json({ message: 'Admin access required' });\n      }\n      \n      const settings = await storage.getAiTherapySettings();\n      res.json(settings);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Get active AI therapy settings\n  app.get('/api/therapy/settings/active', isAuthenticated, async (req, res) => {\n    try {\n      const settings = await storage.getActiveAiTherapySettings();\n      \n      if (!settings) {\n        return res.status(404).json({ message: 'No active settings found' });\n      }\n      \n      res.json(settings);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Create new AI therapy settings\n  app.post('/api/therapy/settings', isAdmin, async (req, res) => {\n    try {\n      const settingsData = insertAiTherapySettingsSchema.parse(req.body);\n      const settings = await storage.createAiTherapySettings(settingsData);\n      res.status(201).json(settings);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Update AI therapy settings\n  app.patch('/api/therapy/settings/:id', isAdmin, async (req, res) => {\n    try {\n      const settingsId = parseInt(req.params.id);\n      const updatedData = req.body;\n      \n      const settings = await storage.updateAiTherapySettings(Number(settingsId), updatedData);\n      \n      if (!settings) {\n        return res.status(404).json({ message: 'Settings not found' });\n      }\n      \n      res.json(settings);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // ------------------------\n  // AUDIO ROUTES\n  // ------------------------\n  \n  // Transcribe audio file\n  app.post('/api/audio/transcribe', isAuthenticated, upload.single('audio'), async (req, res) => {\n    try {\n      if (!req.file) {\n        return res.status(400).json({ message: 'No audio file provided' });\n      }\n      \n      const audioFilePath = req.file.path;\n      const audioBuffer = fs.readFileSync(audioFilePath);\n      \n      const result = await transcribeAudio(audioBuffer, req.file.originalname);\n      \n      // Clean up the temp file\n      fs.unlinkSync(audioFilePath);\n      \n      res.json(result);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // ------------------------\n  // USER ROUTES\n  // ------------------------\n  \n  // Get all users (admin only)\n  app.get('/api/users', isAdmin, async (req, res) => {\n    try {\n      const users = await storage.getAllUsers();\n      res.json(users);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n\n  // ------------------------\n  // ADMIN ROUTES\n  // ------------------------\n\n  // Admin - Get all users\n  app.get('/api/admin/users', isAdmin, async (req, res) => {\n    try {\n      const users = await storage.getAllUsers();\n      res.json(users);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n\n  // Admin - Create new user\n  app.post('/api/admin/users', isAdmin, async (req, res) => {\n    try {\n      const userData = req.body;\n      const newUser = await storage.createUser(userData);\n\n      // If user is a client, create a client record\n      if (userData.userRole === 'client') {\n        await storage.createClient({\n          userId: newUser.id,\n          name: userData.name,\n          status: 'active'\n        });\n      }\n\n      res.status(201).json(newUser);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n\n  // Admin - Update user\n  app.patch('/api/admin/users/:id', isAdmin, async (req, res) => {\n    try {\n      const userId = parseInt(req.params.id);\n      const userData = req.body;\n\n      const updatedUser = await storage.updateUser(userId, userData);\n\n      if (!updatedUser) {\n        return res.status(404).json({ message: 'User not found' });\n      }\n\n      res.json(updatedUser);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n\n  // Admin - Get doctor-client assignments\n  app.get('/api/admin/assignments', isAdmin, async (req, res) => {\n    try {\n      const assignments = await storage.getAllDoctorClientRelationships();\n\n      // Enrich with user names\n      const enrichedAssignments = await Promise.all(\n        assignments.map(async (assignment) => {\n          const doctor = await storage.getUser(assignment.doctorId);\n          const client = await storage.getClient(assignment.clientId);\n\n          return {\n            ...assignment,\n            doctorName: doctor?.name,\n            clientName: client?.name,\n          };\n        })\n      );\n\n      res.json(enrichedAssignments);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n\n  // Admin - Create doctor-client assignment\n  app.post('/api/admin/assignments', isAdmin, async (req, res) => {\n    try {\n      const { doctorId, clientId } = req.body;\n\n      // Validate doctor and client existence\n      const doctor = await storage.getUser(doctorId);\n      const client = await storage.getClient(clientId);\n\n      if (!doctor || doctor.userRole !== 'doctor') {\n        return res.status(400).json({ message: 'Invalid doctor ID' });\n      }\n\n      if (!client) {\n        return res.status(400).json({ message: 'Invalid client ID' });\n      }\n\n      const assignment = await storage.createDoctorClient({ doctorId, clientId });\n      res.status(201).json(assignment);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n\n  // Admin - Get invitation codes\n  app.get('/api/admin/invitation-codes', isAdmin, async (req, res) => {\n    try {\n      const codes = await storage.getAllInvitationCodes();\n\n      // Enrich with user names\n      const enrichedCodes = await Promise.all(\n        codes.map(async (code) => {\n          const creator = await storage.getUser(code.createdBy);\n          const usedBy = code.usedBy ? await storage.getUser(code.usedBy) : null;\n\n          return {\n            ...code,\n            creatorName: creator?.name,\n            usedByName: usedBy?.name,\n          };\n        })\n      );\n\n      res.json(enrichedCodes);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n\n  // Admin - Create invitation code\n  app.post('/api/admin/invitation-codes', isAdmin, async (req, res) => {\n    try {\n      const { type } = req.body;\n\n      // Generate a unique code\n      const generateCode = () => {\n        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n        let result = '';\n        for (let i = 0; i < 8; i++) {\n          result += chars.charAt(Math.floor(Math.random() * chars.length));\n        }\n        return result;\n      };\n\n      let code = generateCode();\n      let attempts = 0;\n\n      // Ensure code is unique\n      while (attempts < 10) {\n        const existing = await storage.getInvitationCodeByCode(code);\n        if (!existing) break;\n        code = generateCode();\n        attempts++;\n      }\n\n      if (attempts >= 10) {\n        return res.status(500).json({ message: 'Failed to generate unique invitation code' });\n      }\n\n      const codeData = {\n        code,\n        type,\n        createdBy: req.user!.id,\n      };\n\n      const invitationCode = await storage.createInvitationCode(codeData);\n      res.status(201).json(invitationCode);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n\n  // ------------------------\n  // ANALYTICS ROUTES\n  // ------------------------\n\n  // Get analytics data\n  app.get('/api/analytics', isAuthenticated, async (req, res) => {\n    try {\n      const userId = req.user!.id;\n      const userRole = req.user!.userRole;\n\n      // Get date range from query params (default to last 30 days)\n      const endDate = new Date();\n      const startDate = new Date();\n      startDate.setDate(startDate.getDate() - 30);\n\n      let analytics;\n\n      if (userRole === 'admin') {\n        // Admin sees system-wide analytics\n        analytics = await getSystemAnalytics(startDate, endDate);\n      } else if (userRole === 'doctor') {\n        // Doctor sees their client analytics\n        analytics = await getDoctorAnalytics(userId, startDate, endDate);\n      } else {\n        // Client sees their own analytics\n        analytics = await getClientAnalytics(userId, startDate, endDate);\n      }\n\n      res.json(analytics);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n\n  // Get weekly activity data\n  app.get('/api/analytics/weekly', isAuthenticated, async (req, res) => {\n    try {\n      const userId = req.user!.id;\n      const userRole = req.user!.userRole;\n\n      const weeklyData = await getWeeklyActivity(userId, userRole);\n      res.json(weeklyData);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n\n  // Get monthly trends\n  app.get('/api/analytics/monthly', isAuthenticated, async (req, res) => {\n    try {\n      const userId = req.user!.id;\n      const userRole = req.user!.userRole;\n\n      const monthlyData = await getMonthlyTrends(userId, userRole);\n      res.json(monthlyData);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Update user\n  app.patch('/api/users/:id', isAuthenticated, async (req, res) => {\n    try {\n      const userId = parseInt(req.params.id);\n      \n      // Only allow users to update themselves or admins to update anyone\n      if (req.user?.id !== userId && req.user?.userRole !== 'admin') {\n        return res.status(403).json({ message: 'Not authorized to update this user' });\n      }\n      \n      const userData = req.body;\n      \n      // Prevent role escalation by non-admins\n      if (userData.userRole && req.user?.userRole !== 'admin') {\n        delete userData.userRole;\n      }\n      \n      const user = await storage.updateUser(Number(userId), userData);\n      \n      if (!user) {\n        return res.status(404).json({ message: 'User not found' });\n      }\n      \n      res.json(user);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // ------------------------\n  // DASHBOARD ROUTES\n  // ------------------------\n  \n  // Get dashboard stats\n  app.get('/api/dashboard/stats', isAuthenticated, async (req, res) => {\n    try {\n      const userId = req.user?.id;\n      \n      if (!userId || isNaN(userId)) {\n        return res.status(401).json({ message: 'User ID is required' });\n      }\n      \n      // Use custom SQL query instead of ORM method\n      const stats = await getDashboardStatsSQL(pool, Number(userId));\n      res.json(stats);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // ------------------------\n  // INVITE CODE ROUTES\n  // ------------------------\n  \n  // Create a new invitation code\n  app.post('/api/invites', isAuthenticated, async (req, res) => {\n    try {\n      // Validate user permissions to create invitation codes\n      if (req.user?.userRole === 'client') {\n        return res.status(403).json({ message: 'Clients cannot create invitation codes' });\n      }\n      \n      if (req.user?.userRole === 'doctor' && req.body.type !== 'client') {\n        return res.status(403).json({ message: 'Doctors can only create client invitation codes' });\n      }\n      \n      // Generate a random code if not provided\n      const code = req.body.code || randomInvitationCode();\n      \n      const inviteData = insertInvitationCodeSchema.parse({\n        ...req.body,\n        code,\n        createdBy: req.user?.id\n      });\n      \n      const invite = await storage.createInvitationCode(inviteData);\n      res.status(201).json(invite);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Get invitation codes created by the user\n  app.get('/api/invites', isAuthenticated, async (req, res) => {\n    try {\n      const userId = req.user?.id;\n      if (!userId || isNaN(userId)) {\n        return res.status(401).json({ message: 'User ID is required' });\n      }\n      \n      const type = req.query.type as 'doctor' | 'client' | undefined;\n      \n      const codes = await storage.getInvitationCodesByCreator(Number(userId), type);\n      res.json(codes);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Check if an invitation code is valid\n  app.get('/api/invites/:code', async (req, res) => {\n    try {\n      const code = req.params.code;\n      const inviteCode = await storage.getInvitationCodeByCode(code);\n      \n      if (!inviteCode) {\n        return res.status(404).json({ message: 'Invitation code not found' });\n      }\n      \n      if (inviteCode.used) {\n        return res.status(400).json({ message: 'Invitation code has already been used' });\n      }\n      \n      if (inviteCode.expiresAt && new Date(inviteCode.expiresAt) < new Date()) {\n        return res.status(400).json({ message: 'Invitation code has expired' });\n      }\n      \n      res.json({\n        valid: true,\n        type: inviteCode.type\n      });\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Create HTTP server\n  const httpServer = createServer(app);\n  \n  // Test voice endpoint\n  app.get('/api/test-voice', async (req, res) => {\n    try {\n      // Extract parameters from query\n      const voice = req.query.voice?.toString() || 'shimmer';\n      const speed = parseFloat(req.query.speed?.toString() || '1.0');\n      \n      // Test phrases for each voice\n      const testPhrases: Record<string, string> = {\n        'alloy': 'This is a test of the Alloy voice. Can you hear this clearly?',\n        'echo': 'Testing the Echo voice. Please confirm if the audio is working properly.',\n        'fable': 'The Fable voice is being tested now. Is the sound quality good?',\n        'onyx': 'This is the Onyx voice test. Can you hear this message?',\n        'nova': 'Nova voice test in progress. Please verify the audio output.',\n        'shimmer': 'Testing the Shimmer voice output. Is this coming through clearly?'\n      };\n      \n      const text = testPhrases[voice] || 'This is a test of the voice output system. Can you hear this clearly?';\n      \n      console.log(`Processing test audio request: ${text}`);\n      \n      // Ensure uploads directory exists\n      const uploadsDir = path.join(process.cwd(), 'uploads', 'audio');\n      if (!fs.existsSync(uploadsDir)) {\n        fs.mkdirSync(uploadsDir, { recursive: true });\n      }\n      \n      // Generate unique filename\n      const filename = `speech-${uuidv4()}.mp3`;\n      const filePath = path.join(uploadsDir, filename);\n      const fileUrl = `/uploads/audio/${filename}`;\n      \n      // Create an instance of the OpenAI client\n      const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });\n      \n      // Generate speech using OpenAI API\n      const mp3Response = await openai.audio.speech.create({\n        model: \"tts-1\",\n        voice: voice as any,\n        input: text,\n        speed: speed\n      });\n      \n      // Convert to buffer and save to file\n      const buffer = Buffer.from(await mp3Response.arrayBuffer());\n      fs.writeFileSync(filePath, buffer);\n      \n      console.log(`Sent test audio response with URL: ${fileUrl}`);\n      \n      // Return URL to audio file\n      res.json({ url: fileUrl });\n      \n    } catch (error) {\n      console.error('Error generating test voice:', error);\n      res.status(500).json({ \n        error: 'Failed to generate test voice', \n        details: error instanceof Error ? error.message : String(error)\n      });\n    }\n  });\n\n  // WebSocket server is set up in index.ts\n\n  return httpServer;\n}\n\n// ------------------------\n// ANALYTICS HELPER FUNCTIONS\n// ------------------------\n\nasync function getSystemAnalytics(startDate: Date, endDate: Date) {\n  // Get total conversations, users, and other system metrics\n  const conversations = await storage.getAllConversations();\n  const users = await storage.getAllUsers();\n  const clients = await storage.getAllClients();\n\n  // Filter conversations by date range\n  const recentConversations = conversations.filter(conv => {\n    const convDate = new Date(conv.createdAt);\n    return convDate >= startDate && convDate <= endDate;\n  });\n\n  // Calculate metrics\n  const totalSessions = recentConversations.length;\n  const totalUsers = users.length;\n  const totalClients = clients.length;\n  const activeDoctors = users.filter(u => u.userRole === 'doctor').length;\n\n  // Calculate average session duration (if available)\n  const avgSessionDuration = recentConversations.length > 0\n    ? recentConversations.reduce((sum, conv) => {\n        if (conv.endedAt && conv.createdAt) {\n          const duration = new Date(conv.endedAt).getTime() - new Date(conv.createdAt).getTime();\n          return sum + duration;\n        }\n        return sum;\n      }, 0) / recentConversations.length / 1000 / 60 // Convert to minutes\n    : 0;\n\n  return {\n    totalSessions,\n    totalUsers,\n    totalClients,\n    activeDoctors,\n    avgSessionDuration: Math.round(avgSessionDuration),\n    sessionGrowth: calculateGrowthRate(conversations, startDate, endDate),\n    userGrowth: calculateUserGrowthRate(users, startDate, endDate)\n  };\n}\n\nasync function getDoctorAnalytics(doctorId: number, startDate: Date, endDate: Date) {\n  // Get doctor's clients and their conversations\n  const doctorClients = await storage.getClientsByDoctor(doctorId);\n  const clientIds = doctorClients.map(c => c.id);\n\n  // Get conversations for doctor's clients\n  const allConversations = await storage.getAllConversations();\n  const doctorConversations = allConversations.filter(conv =>\n    clientIds.includes(conv.clientId) &&\n    new Date(conv.createdAt) >= startDate &&\n    new Date(conv.createdAt) <= endDate\n  );\n\n  const totalSessions = doctorConversations.length;\n  const activeClients = new Set(doctorConversations.map(c => c.clientId)).size;\n  const totalClients = doctorClients.length;\n\n  // Calculate average session duration\n  const avgSessionDuration = doctorConversations.length > 0\n    ? doctorConversations.reduce((sum, conv) => {\n        if (conv.endedAt && conv.createdAt) {\n          const duration = new Date(conv.endedAt).getTime() - new Date(conv.createdAt).getTime();\n          return sum + duration;\n        }\n        return sum;\n      }, 0) / doctorConversations.length / 1000 / 60\n    : 0;\n\n  return {\n    totalSessions,\n    activeClients,\n    totalClients,\n    avgSessionDuration: Math.round(avgSessionDuration),\n    sessionGrowth: calculateGrowthRate(doctorConversations, startDate, endDate)\n  };\n}\n\nasync function getClientAnalytics(clientId: number, startDate: Date, endDate: Date) {\n  // Get client's conversations\n  const allConversations = await storage.getAllConversations();\n  const clientConversations = allConversations.filter(conv =>\n    conv.clientId === clientId &&\n    new Date(conv.createdAt) >= startDate &&\n    new Date(conv.createdAt) <= endDate\n  );\n\n  const totalSessions = clientConversations.length;\n\n  // Calculate total session time\n  const totalSessionTime = clientConversations.reduce((sum, conv) => {\n    if (conv.endedAt && conv.createdAt) {\n      const duration = new Date(conv.endedAt).getTime() - new Date(conv.createdAt).getTime();\n      return sum + duration;\n    }\n    return sum;\n  }, 0) / 1000 / 60; // Convert to minutes\n\n  const avgSessionDuration = totalSessions > 0 ? totalSessionTime / totalSessions : 0;\n\n  // Get recent session dates for streak calculation\n  const sessionDates = clientConversations\n    .map(conv => new Date(conv.createdAt).toDateString())\n    .filter((date, index, arr) => arr.indexOf(date) === index)\n    .sort();\n\n  return {\n    totalSessions,\n    totalSessionTime: Math.round(totalSessionTime),\n    avgSessionDuration: Math.round(avgSessionDuration),\n    sessionStreak: calculateSessionStreak(sessionDates),\n    recentActivity: sessionDates.slice(-7) // Last 7 unique session dates\n  };\n}\n\nasync function getWeeklyActivity(userId: number, userRole: string) {\n  const endDate = new Date();\n  const startDate = new Date();\n  startDate.setDate(startDate.getDate() - 49); // 7 weeks\n\n  const allConversations = await storage.getAllConversations();\n  let relevantConversations;\n\n  if (userRole === 'admin') {\n    relevantConversations = allConversations;\n  } else if (userRole === 'doctor') {\n    const doctorClients = await storage.getClientsByDoctor(userId);\n    const clientIds = doctorClients.map(c => c.id);\n    relevantConversations = allConversations.filter(conv => clientIds.includes(conv.clientId));\n  } else {\n    relevantConversations = allConversations.filter(conv => conv.clientId === userId);\n  }\n\n  // Group by week\n  const weeklyData = [];\n  for (let i = 6; i >= 0; i--) {\n    const weekStart = new Date();\n    weekStart.setDate(weekStart.getDate() - (i * 7));\n    weekStart.setHours(0, 0, 0, 0);\n\n    const weekEnd = new Date(weekStart);\n    weekEnd.setDate(weekEnd.getDate() + 6);\n    weekEnd.setHours(23, 59, 59, 999);\n\n    const weekConversations = relevantConversations.filter(conv => {\n      const convDate = new Date(conv.createdAt);\n      return convDate >= weekStart && convDate <= weekEnd;\n    });\n\n    weeklyData.push({\n      week: `Week ${7 - i}`,\n      sessions: weekConversations.length,\n      date: weekStart.toISOString().split('T')[0]\n    });\n  }\n\n  return weeklyData;\n}\n\nasync function getMonthlyTrends(userId: number, userRole: string) {\n  const endDate = new Date();\n  const startDate = new Date();\n  startDate.setMonth(startDate.getMonth() - 11); // 12 months\n\n  const allConversations = await storage.getAllConversations();\n  let relevantConversations;\n\n  if (userRole === 'admin') {\n    relevantConversations = allConversations;\n  } else if (userRole === 'doctor') {\n    const doctorClients = await storage.getClientsByDoctor(userId);\n    const clientIds = doctorClients.map(c => c.id);\n    relevantConversations = allConversations.filter(conv => clientIds.includes(conv.clientId));\n  } else {\n    relevantConversations = allConversations.filter(conv => conv.clientId === userId);\n  }\n\n  // Group by month\n  const monthlyData = [];\n  for (let i = 11; i >= 0; i--) {\n    const monthStart = new Date();\n    monthStart.setMonth(monthStart.getMonth() - i);\n    monthStart.setDate(1);\n    monthStart.setHours(0, 0, 0, 0);\n\n    const monthEnd = new Date(monthStart);\n    monthEnd.setMonth(monthEnd.getMonth() + 1);\n    monthEnd.setDate(0);\n    monthEnd.setHours(23, 59, 59, 999);\n\n    const monthConversations = relevantConversations.filter(conv => {\n      const convDate = new Date(conv.createdAt);\n      return convDate >= monthStart && convDate <= monthEnd;\n    });\n\n    monthlyData.push({\n      month: monthStart.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),\n      sessions: monthConversations.length,\n      date: monthStart.toISOString().split('T')[0]\n    });\n  }\n\n  return monthlyData;\n}\n\nfunction calculateGrowthRate(conversations: any[], startDate: Date, endDate: Date) {\n  const midDate = new Date((startDate.getTime() + endDate.getTime()) / 2);\n\n  const firstHalf = conversations.filter(conv => {\n    const convDate = new Date(conv.createdAt);\n    return convDate >= startDate && convDate < midDate;\n  }).length;\n\n  const secondHalf = conversations.filter(conv => {\n    const convDate = new Date(conv.createdAt);\n    return convDate >= midDate && convDate <= endDate;\n  }).length;\n\n  if (firstHalf === 0) return secondHalf > 0 ? 100 : 0;\n  return Math.round(((secondHalf - firstHalf) / firstHalf) * 100);\n}\n\nfunction calculateUserGrowthRate(users: any[], startDate: Date, endDate: Date) {\n  const midDate = new Date((startDate.getTime() + endDate.getTime()) / 2);\n\n  const firstHalf = users.filter(user => {\n    const userDate = new Date(user.createdAt);\n    return userDate >= startDate && userDate < midDate;\n  }).length;\n\n  const secondHalf = users.filter(user => {\n    const userDate = new Date(user.createdAt);\n    return userDate >= midDate && userDate <= endDate;\n  }).length;\n\n  if (firstHalf === 0) return secondHalf > 0 ? 100 : 0;\n  return Math.round(((secondHalf - firstHalf) / firstHalf) * 100);\n}\n\nfunction calculateSessionStreak(sessionDates: string[]) {\n  if (sessionDates.length === 0) return 0;\n\n  let streak = 1;\n  let maxStreak = 1;\n\n  for (let i = 1; i < sessionDates.length; i++) {\n    const prevDate = new Date(sessionDates[i - 1]);\n    const currDate = new Date(sessionDates[i]);\n    const dayDiff = (currDate.getTime() - prevDate.getTime()) / (1000 * 60 * 60 * 24);\n\n    if (dayDiff <= 7) { // Within a week\n      streak++;\n      maxStreak = Math.max(maxStreak, streak);\n    } else {\n      streak = 1;\n    }\n  }\n\n  return maxStreak;\n}\n", "modifiedCode": "import { Express, Request, Response, NextFunction } from 'express';\nimport { Server as HttpServer, createServer } from 'http';\nimport { WebSocket } from 'ws';\nimport multer from 'multer';\nimport rateLimit from 'express-rate-limit';\nimport { body, validationResult } from 'express-validator';\nimport csrf from 'csrf';\nimport path from 'path';\nimport fs from 'fs';\nimport { v4 as uuidv4 } from 'uuid';\nimport OpenAI from 'openai';\nimport {\n  analyzeTherapyNotes,\n  transcribeAudio,\n  streamAITherapistResponse\n} from './openai';\nimport { storage } from './storage';\nimport {\n  insertSessionNoteSchema,\n  insertClientThemeSchema,\n  insertAiTherapySettingsSchema,\n  insertInvitationCodeSchema\n} from '@shared/schema';\nimport { getDashboardStatsSQL, getRecentSessionNotesSQL } from './custom-queries';\nimport { pool } from './db';\nimport { setupAuth } from './auth';\nimport { directLogin<PERSON><PERSON><PERSON>, createAdminUser } from './temp-login-fix';\nimport {\n  Client,\n  ConversationMessage\n} from './types';\n// WebSocket handler is imported in index.ts\n\n// Set up multer for file upload handling\nconst uploadsDir = path.join(process.cwd(), 'uploads');\n\n// Create uploads directory if it doesn't exist\nif (!fs.existsSync(uploadsDir)) {\n  fs.mkdirSync(uploadsDir, { recursive: true });\n}\n\nconst upload = multer({\n  storage: multer.diskStorage({\n    destination: (_req, _file, cb) => {\n      cb(null, uploadsDir);\n    },\n    filename: (_req, file, cb) => {\n      // Create unique filename with timestamp and original extension\n      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);\n      cb(null, uniqueSuffix + path.extname(file.originalname));\n    }\n  }),\n  limits: {\n    fileSize: 50 * 1024 * 1024, // 50 MB max file size\n  }\n});\n\n// Temporary in-memory store for voice therapy sessions\n// Use this when database connection fails\n// Note: These are currently unused but kept for potential fallback functionality\n\ndeclare global {\n  namespace Express {\n    interface User {\n      id: number;\n      username: string;\n      [key: string]: any;\n    }\n  }\n}\n\n// Add custom properties to express-session\ndeclare module 'express-session' {\n  interface SessionData {\n    csrfSecret: string;\n  }\n}\n\nfunction randomInvitationCode(length = 8): string {\n  const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += characters.charAt(Math.floor(Math.random() * characters.length));\n  }\n  return result;\n}\n\nexport async function registerRoutes(app: Express): Promise<HttpServer> {\n  // Create admin user if needed\n  try {\n    await createAdminUser();\n    console.log('[STARTUP] Admin user check complete');\n  } catch (error) {\n    console.error('[STARTUP] Error checking/creating admin user:', error);\n  }\n\n  // SECURITY: Add rate limiting\n  const authLimiter = rateLimit({\n    windowMs: 15 * 60 * 1000, // 15 minutes\n    max: 5, // Limit each IP to 5 requests per windowMs for auth routes\n    message: 'Too many authentication attempts, please try again later.',\n    standardHeaders: true,\n    legacyHeaders: false,\n  });\n\n  const generalLimiter = rateLimit({\n    windowMs: 1 * 60 * 1000, // 1 minutes\n    max: process.env.NODE_ENV === 'development' ? 1000 : 1000, // possiblyHigher limit for development\n    message: 'Too many requests, please try again later.',\n    standardHeaders: true,\n    legacyHeaders: false,\n  });\n\n  // Apply general rate limiting to all routes\n  app.use(generalLimiter);\n\n  // CSRF Protection\n  const tokens = new csrf();\n  app.use((req, _res, next) => {\n    if (!req.session.csrfSecret) {\n      req.session.csrfSecret = tokens.secretSync();\n    }\n    next();\n  });\n\n  // CSRF token endpoint\n  app.get('/api/csrf-token', (req, res) => {\n    const csrfSecret = req.session.csrfSecret;\n    if (!csrfSecret) {\n      return res.status(500).json({ message: 'CSRF secret not initialized' });\n    }\n    const token = tokens.create(csrfSecret);\n    res.json({ csrfToken: token });\n  });\n\n  // CSRF validation middleware (currently unused but kept for potential use)\n  const _validateCSRF = (req: Request, res: Response, next: NextFunction) => {\n    if (req.method === 'GET' || req.method === 'HEAD' || req.method === 'OPTIONS') {\n      return next();\n    }\n\n    const token = req.headers['x-csrf-token'] || req.body._csrf;\n    const csrfSecret = req.session.csrfSecret;\n    if (!token || !csrfSecret || !tokens.verify(csrfSecret, token.toString())) {\n      return res.status(403).json({ message: 'Invalid CSRF token' });\n    }\n    next();\n  };\n\n  // SECURITY: Add security headers\n  app.use((req, res, next) => {\n    // Prevent clickjacking\n    res.setHeader('X-Frame-Options', 'DENY');\n    // Prevent MIME type sniffing\n    res.setHeader('X-Content-Type-Options', 'nosniff');\n    // Enable XSS protection\n    res.setHeader('X-XSS-Protection', '1; mode=block');\n    // Referrer policy\n    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');\n    // Content Security Policy\n    res.setHeader('Content-Security-Policy', \"default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:;\");\n    // Strict Transport Security (for HTTPS)\n    if (req.secure) {\n      res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');\n    }\n    next();\n  });\n\n  // Set up standard auth routes (/api/register, /api/login, /api/logout, /api/user)\n  setupAuth(app);\n\n  // Apply auth rate limiting to authentication routes\n  app.use('/api/login', authLimiter);\n  app.use('/api/register', authLimiter);\n  app.use('/api/direct-login', authLimiter);\n\n  // Middleware to handle validation errors\n  const handleValidationErrors = (req: Request, res: Response, next: NextFunction) => {\n    const errors = validationResult(req);\n    if (!errors.isEmpty()) {\n      console.log(`[SECURITY] Validation failed for ${req.method} ${req.path}:`, errors.array());\n      return res.status(400).json({\n        message: 'Validation failed',\n        errors: errors.array()\n      });\n    }\n    next();\n  };\n\n  // Input validation for authentication\n  const authValidation = [\n    body('username').isLength({ min: 1 }).trim().escape().withMessage('Username is required'),\n    body('password').isLength({ min: 1 }).withMessage('Password is required'),\n    handleValidationErrors\n  ];\n\n  // Add direct login route that bypasses Passport.js\n  app.post('/api/direct-login', authValidation, directLoginHandler);\n  console.log('[STARTUP] Direct login route registered at /api/direct-login');\n  \n  const isAuthenticated = (req: Request, res: Response, next: NextFunction) => {\n    if (req.isAuthenticated()) {\n      return next();\n    }\n    res.status(401).json({ message: 'Not authenticated' });\n  };\n\n  // Security audit logging utility (available for future use)\n  // const auditLog = (action: string, userId?: number, details?: any) => {\n  //   const timestamp = new Date().toISOString();\n  //   console.log(`[AUDIT] ${timestamp} - ${action} - User: ${userId || 'anonymous'} - Details:`, details);\n  // };\n  \n  const isAdmin = (req: Request, res: Response, next: NextFunction) => {\n    if (req.isAuthenticated() && req.user && req.user.userRole === 'admin') {\n      return next();\n    }\n    res.status(403).json({ message: 'Admin access required' });\n  };\n  \n  const handleError = (res: Response, error: unknown) => {\n    console.error('API Error:', error);\n\n    // Don't leak sensitive error information in production\n    if (process.env.NODE_ENV === 'production') {\n      res.status(500).json({ message: 'Internal server error' });\n    } else {\n      res.status(500).json({ message: error instanceof Error ? error.message : 'Server error' });\n    }\n  };\n  \n  // ------------------------\n  // CLIENTS ROUTES\n  // ------------------------\n  \n  // Get all clients for a doctor\n  app.get('/api/clients', isAuthenticated, async (req, res) => {\n    try {\n      const userId = req.user?.id;\n      \n      if (!userId || isNaN(userId)) {\n        return res.status(401).json({ message: 'User ID is required' });\n      }\n      \n      const user = await storage.getUser(Number(userId));\n      \n      if (!user) {\n        return res.status(404).json({ message: 'User not found' });\n      }\n      \n      let clients: Client[] = [];\n\n      if (user.userRole === 'admin') {\n        // Admin can see all clients\n        clients = await storage.getAllClients();\n      } else if (user.userRole === 'doctor') {\n        // Doctor can only see their assigned clients\n        clients = await storage.getClientsByDoctor(Number(userId));\n      } else if (user.userRole === 'client') {\n        // Client can only see themselves\n        const client = await storage.getClientByUserId(Number(userId));\n        if (client) {\n          clients = [client];\n        }\n      }\n      \n      res.json(clients);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Create a client\n  app.post('/api/clients', isAuthenticated, async (req, res) => {\n    try {\n      if (req.user?.userRole !== 'doctor' && req.user?.userRole !== 'admin') {\n        return res.status(403).json({ message: 'Not authorized to create clients' });\n      }\n      \n      const clientData = req.body;\n      const client = await storage.createClient(clientData);\n      \n      // Assign doctor-client relationship\n      if (req.user?.userRole === 'doctor') {\n        await storage.createDoctorClient({\n          doctorId: req.user.id,\n          clientId: client.userId\n        });\n      }\n      \n      res.status(201).json(client);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Update client status\n  app.patch('/api/clients/:id/status', isAuthenticated, async (req, res) => {\n    try {\n      const clientId = parseInt(req.params.id);\n      const { status } = req.body;\n      \n      if (!status) {\n        return res.status(400).json({ message: 'Status is required' });\n      }\n      \n      const client = await storage.updateClientStatus(Number(clientId), status);\n      \n      if (!client) {\n        return res.status(404).json({ message: 'Client not found' });\n      }\n      \n      res.json(client);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // ------------------------\n  // SESSION NOTES ROUTES\n  // ------------------------\n  \n  // Get all session notes for logged in user (doctor or client)\n  app.get('/api/notes', isAuthenticated, async (req, res) => {\n    try {\n      const userId = req.user?.id;\n      \n      if (!userId || isNaN(userId)) {\n        return res.status(401).json({ message: 'User ID is required' });\n      }\n      \n      // Pagination parameters\n      const page = parseInt(req.query.page as string) || 1;\n      const limit = parseInt(req.query.limit as string) || 10;\n      const clientId = req.query.clientId ? parseInt(req.query.clientId as string) : undefined;\n      \n      // Get notes based on user role\n      let notes = [];\n      \n      if (clientId) {\n        // If a specific client ID is requested\n        notes = await storage.getSessionNotesByClientId(Number(clientId));\n      } else {\n        // Get recent notes for the user using SQL directly\n        notes = await getRecentSessionNotesSQL(pool, Number(userId), limit);\n      }\n      \n      res.json(notes);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Get a specific session note\n  app.get('/api/notes/:id', isAuthenticated, async (req, res) => {\n    try {\n      const noteId = parseInt(req.params.id);\n      const note = await storage.getSessionNote(Number(noteId));\n      \n      if (!note) {\n        return res.status(404).json({ message: 'Note not found' });\n      }\n      \n      // Check if user has access to this note\n      const userId = req.user?.id;\n      const userRole = req.user?.userRole;\n      \n      if (userRole === 'admin') {\n        // Admin can access any note\n      } else if (userRole === 'doctor') {\n        // Doctor can access notes for their clients\n        const clients = await storage.getClientsByUserId(Number(userId));\n        const clientIds = clients.map(c => c.id);\n        \n        if (!clientIds.includes(note.clientId)) {\n          return res.status(403).json({ message: 'Not authorized to access this note' });\n        }\n      } else if (userRole === 'client') {\n        // Client can only access their own notes\n        const client = await storage.getClientByUserId(Number(userId));\n        \n        if (!client || note.clientId !== client.id) {\n          return res.status(403).json({ message: 'Not authorized to access this note' });\n        }\n      }\n      \n      res.json(note);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Input validation for notes\n  const noteValidation = [\n    body('content').isLength({ min: 1, max: 10000 }).trim().withMessage('Content is required and must be less than 10000 characters'),\n    body('clientId').isInt({ min: 1 }).withMessage('Valid client ID is required'),\n    body('sessionDate').isISO8601().withMessage('Valid session date is required'),\n    body('duration').optional().isInt({ min: 1, max: 480 }).withMessage('Duration must be between 1 and 480 minutes'),\n    handleValidationErrors\n  ];\n\n  // Create a new session note\n  app.post('/api/notes', isAuthenticated, noteValidation, async (req, res) => {\n    try {\n      if (req.user?.userRole !== 'doctor' && req.user?.userRole !== 'admin') {\n        return res.status(403).json({ message: 'Not authorized to create notes' });\n      }\n      \n      const noteData = insertSessionNoteSchema.parse({\n        ...req.body,\n        createdBy: req.user.id\n      });\n      \n      const note = await storage.createSessionNote(noteData);\n      \n      // Process with AI in the background\n      analyzeTherapyNotes(note.content)\n        .then(async (analysis) => {\n          // Update the note with AI analysis\n          await storage.updateSessionNoteAnalysis(\n            note.id,\n            analysis.summary,\n            analysis.themes,\n            analysis.recommendations\n          );\n          \n          // Update client themes based on analysis\n          if (analysis.themes) {\n            for (const theme of analysis.themes) {\n              await storage.updateOrCreateClientTheme(\n                note.clientId,\n                theme.name,\n                theme.trend\n              );\n            }\n          }\n        })\n        .catch(err => {\n          console.error('Error analyzing notes:', err);\n        });\n      \n      res.status(201).json(note);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // ------------------------\n  // THEME ROUTES\n  // ------------------------\n  \n  // Get themes for a client\n  app.get('/api/clients/:id/themes', isAuthenticated, async (req, res) => {\n    try {\n      const clientId = parseInt(req.params.id);\n\n      // SECURITY: Check access permissions\n      const userId = req.user!.id;\n      const userRole = req.user!.userRole;\n\n      if (userRole === 'admin') {\n        // Admin can access all client themes\n      } else if (userRole === 'doctor') {\n        // Doctor can only access themes for their assigned clients\n        const doctorClients = await storage.getClientsByDoctor(userId);\n        const hasAccess = doctorClients.some(c => c.id === clientId);\n\n        if (!hasAccess) {\n          return res.status(403).json({ message: 'Access denied: Not your assigned client' });\n        }\n      } else if (userRole === 'client') {\n        // Client can only access their own themes\n        const client = await storage.getClientByUserId(userId);\n        if (!client || client.id !== clientId) {\n          return res.status(403).json({ message: 'Access denied: Not your themes' });\n        }\n      } else {\n        return res.status(403).json({ message: 'Access denied: Invalid role' });\n      }\n\n      const themes = await storage.getClientThemes(Number(clientId));\n      res.json(themes);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Create or update a theme for a client\n  app.post('/api/clients/:id/themes', isAuthenticated, async (req, res) => {\n    try {\n      if (req.user?.userRole !== 'doctor' && req.user?.userRole !== 'admin') {\n        return res.status(403).json({ message: 'Not authorized to manage themes' });\n      }\n      \n      const clientId = parseInt(req.params.id);\n      if (!clientId || isNaN(clientId)) {\n        return res.status(400).json({ message: 'Client ID is required' });\n      }\n      \n      const { name, trend } = insertClientThemeSchema.parse(req.body);\n      const trendNumber = Number(trend);\n      \n      if (isNaN(trendNumber)) {\n        return res.status(400).json({ message: 'Trend must be a number' });\n      }\n      \n      const theme = await storage.updateOrCreateClientTheme(clientId, name, trendNumber);\n      res.status(201).json(theme);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // ------------------------\n  // AI THERAPY ROUTES\n  // ------------------------\n  \n  // Get all AI therapy conversations for a user\n  app.get('/api/therapy/conversations', isAuthenticated, async (req, res) => {\n    try {\n      const userId = req.user?.id;\n      if (!userId || isNaN(userId)) {\n        return res.status(401).json({ message: 'User ID is required' });\n      }\n      \n      const clientIdParam = req.query.clientId ? parseInt(req.query.clientId as string) : undefined;\n      const clientId = clientIdParam && !isNaN(clientIdParam) ? Number(clientIdParam) : undefined;\n      \n      const conversations = await storage.getAiTherapyConversations(Number(userId), clientId);\n      res.json(conversations);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Get a specific AI therapy conversation\n  app.get('/api/therapy/conversations/:id', isAuthenticated, async (req, res) => {\n    try {\n      const conversationId = parseInt(req.params.id);\n      const conversation = await storage.getAiTherapyConversation(Number(conversationId));\n\n      if (!conversation) {\n        return res.status(404).json({ message: 'Conversation not found' });\n      }\n\n      // SECURITY: Check access permissions\n      const userId = req.user!.id;\n      const userRole = req.user!.userRole;\n\n      if (userRole === 'admin') {\n        // Admin can access all conversations\n      } else if (userRole === 'doctor') {\n        // Doctor can only access conversations of their assigned clients\n        const client = await storage.getClient(conversation.clientId);\n        if (!client) {\n          return res.status(404).json({ message: 'Client not found' });\n        }\n\n        const doctorClients = await storage.getClientsByDoctor(userId);\n        const hasAccess = doctorClients.some(c => c.id === conversation.clientId);\n\n        if (!hasAccess) {\n          return res.status(403).json({ message: 'Access denied: Not your assigned client' });\n        }\n      } else {\n        // Client can only access their own conversations\n        if (conversation.clientId !== userId) {\n          return res.status(403).json({ message: 'Access denied: Not your conversation' });\n        }\n      }\n\n      // Get messages for this conversation\n      const messages = await storage.getAiTherapyMessages(Number(conversationId));\n\n      res.json({\n        conversation,\n        messages\n      });\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Create a new AI therapy conversation\n  app.post('/api/therapy/conversations', isAuthenticated, async (req, res) => {\n    try {\n      const conversationData = {\n        ...req.body,\n        userId: req.user?.id\n      };\n      \n      const conversation = await storage.createAiTherapyConversation(conversationData);\n      res.status(201).json(conversation);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // End an AI therapy conversation\n  app.post('/api/therapy/conversations/:id/end', isAuthenticated, async (req, res) => {\n    try {\n      const conversationId = parseInt(req.params.id);\n      const { summary } = req.body;\n\n      if (!summary) {\n        return res.status(400).json({ message: 'Summary is required' });\n      }\n\n      // SECURITY: Check access permissions before ending conversation\n      const existingConversation = await storage.getAiTherapyConversation(Number(conversationId));\n      if (!existingConversation) {\n        return res.status(404).json({ message: 'Conversation not found' });\n      }\n\n      const userId = req.user!.id;\n      const userRole = req.user!.userRole;\n\n      if (userRole === 'admin') {\n        // Admin can end any conversation\n      } else if (userRole === 'doctor') {\n        // Doctor can only end conversations of their assigned clients\n        const doctorClients = await storage.getClientsByDoctor(userId);\n        const hasAccess = doctorClients.some(c => c.id === existingConversation.clientId);\n\n        if (!hasAccess) {\n          return res.status(403).json({ message: 'Access denied: Not your assigned client' });\n        }\n      } else {\n        // Client can only end their own conversations\n        if (existingConversation.clientId !== userId) {\n          return res.status(403).json({ message: 'Access denied: Not your conversation' });\n        }\n      }\n\n      const conversation = await storage.endAiTherapyConversation(Number(conversationId), summary);\n\n      if (!conversation) {\n        return res.status(404).json({ message: 'Conversation not found' });\n      }\n\n      res.json(conversation);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Send a message in an AI therapy conversation\n  app.post('/api/therapy/conversations/:id/messages', isAuthenticated, async (req, res) => {\n    try {\n      const conversationId = parseInt(req.params.id);\n      const { content, role } = req.body;\n\n      if (!content) {\n        return res.status(400).json({ message: 'Content is required' });\n      }\n\n      // SECURITY: Check access permissions before sending message\n      const conversation = await storage.getAiTherapyConversation(Number(conversationId));\n      if (!conversation) {\n        return res.status(404).json({ message: 'Conversation not found' });\n      }\n\n      const userId = req.user!.id;\n      const userRole = req.user!.userRole;\n\n      if (userRole === 'admin') {\n        // Admin can send messages in any conversation\n      } else if (userRole === 'doctor') {\n        // Doctor can only send messages in conversations of their assigned clients\n        const doctorClients = await storage.getClientsByDoctor(userId);\n        const hasAccess = doctorClients.some(c => c.id === conversation.clientId);\n\n        if (!hasAccess) {\n          return res.status(403).json({ message: 'Access denied: Not your assigned client' });\n        }\n      } else {\n        // Client can only send messages in their own conversations\n        if (conversation.clientId !== userId) {\n          return res.status(403).json({ message: 'Access denied: Not your conversation' });\n        }\n      }\n      \n      // Create user message\n      const message = await storage.createAiTherapyMessage({\n        conversationId,\n        content,\n        role: role || 'user',\n        timestamp: new Date()\n      });\n      \n      // Get AI response\n      const rawMessages = await storage.getAiTherapyMessages(conversationId);\n      const conversationHistory: ConversationMessage[] = rawMessages.map(msg => ({\n        role: msg.role as 'user' | 'assistant',\n        content: msg.content,\n        timestamp: msg.timestamp\n      }));\n      \n      const settings = await storage.getActiveAiTherapySettings();\n      \n      // Create a temporary WebSocket for streaming\n      const ws = new WebSocket('ws://localhost:3000/ws');\n      \n      // Wait for connection\n      await new Promise((resolve) => ws.on('open', resolve));\n      \n      // Get AI response\n      const aiResponse = await streamAITherapistResponse(\n        ws,\n        content,\n        conversationHistory,\n        true,\n        settings?.systemPrompt\n      );\n      \n      // Save AI response\n      const aiMessage = await storage.createAiTherapyMessage({\n        conversationId,\n        content: aiResponse.message,\n        role: 'assistant',\n        timestamp: new Date()\n      });\n      \n      res.status(201).json({\n        userMessage: message,\n        aiMessage\n      });\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // ------------------------\n  // AI SETTINGS ROUTES\n  // ------------------------\n  \n  // Get all AI therapy settings\n  app.get('/api/therapy/settings', isAuthenticated, async (req, res) => {\n    try {\n      if (req.user?.userRole !== 'admin') {\n        return res.status(403).json({ message: 'Admin access required' });\n      }\n      \n      const settings = await storage.getAiTherapySettings();\n      res.json(settings);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Get active AI therapy settings\n  app.get('/api/therapy/settings/active', isAuthenticated, async (req, res) => {\n    try {\n      const settings = await storage.getActiveAiTherapySettings();\n      \n      if (!settings) {\n        return res.status(404).json({ message: 'No active settings found' });\n      }\n      \n      res.json(settings);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Create new AI therapy settings\n  app.post('/api/therapy/settings', isAdmin, async (req, res) => {\n    try {\n      const settingsData = insertAiTherapySettingsSchema.parse(req.body);\n      const settings = await storage.createAiTherapySettings(settingsData);\n      res.status(201).json(settings);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Update AI therapy settings\n  app.patch('/api/therapy/settings/:id', isAdmin, async (req, res) => {\n    try {\n      const settingsId = parseInt(req.params.id);\n      const updatedData = req.body;\n      \n      const settings = await storage.updateAiTherapySettings(Number(settingsId), updatedData);\n      \n      if (!settings) {\n        return res.status(404).json({ message: 'Settings not found' });\n      }\n      \n      res.json(settings);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // ------------------------\n  // AUDIO ROUTES\n  // ------------------------\n  \n  // Transcribe audio file\n  app.post('/api/audio/transcribe', isAuthenticated, upload.single('audio'), async (req, res) => {\n    try {\n      if (!req.file) {\n        return res.status(400).json({ message: 'No audio file provided' });\n      }\n      \n      const audioFilePath = req.file.path;\n      const audioBuffer = fs.readFileSync(audioFilePath);\n      \n      const result = await transcribeAudio(audioBuffer, req.file.originalname);\n      \n      // Clean up the temp file\n      fs.unlinkSync(audioFilePath);\n      \n      res.json(result);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // ------------------------\n  // USER ROUTES\n  // ------------------------\n  \n  // Get all users (admin only)\n  app.get('/api/users', isAdmin, async (req, res) => {\n    try {\n      const users = await storage.getAllUsers();\n      res.json(users);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n\n  // ------------------------\n  // ADMIN ROUTES\n  // ------------------------\n\n  // Admin - Get all users\n  app.get('/api/admin/users', isAdmin, async (req, res) => {\n    try {\n      const users = await storage.getAllUsers();\n      res.json(users);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n\n  // Admin - Create new user\n  app.post('/api/admin/users', isAdmin, async (req, res) => {\n    try {\n      const userData = req.body;\n      const newUser = await storage.createUser(userData);\n\n      // If user is a client, create a client record\n      if (userData.userRole === 'client') {\n        await storage.createClient({\n          userId: newUser.id,\n          name: userData.name,\n          status: 'active'\n        });\n      }\n\n      res.status(201).json(newUser);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n\n  // Admin - Update user\n  app.patch('/api/admin/users/:id', isAdmin, async (req, res) => {\n    try {\n      const userId = parseInt(req.params.id);\n      const userData = req.body;\n\n      const updatedUser = await storage.updateUser(userId, userData);\n\n      if (!updatedUser) {\n        return res.status(404).json({ message: 'User not found' });\n      }\n\n      res.json(updatedUser);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n\n  // Admin - Get doctor-client assignments\n  app.get('/api/admin/assignments', isAdmin, async (req, res) => {\n    try {\n      const assignments = await storage.getAllDoctorClientRelationships();\n\n      // Enrich with user names\n      const enrichedAssignments = await Promise.all(\n        assignments.map(async (assignment) => {\n          const doctor = await storage.getUser(assignment.doctorId);\n          const client = await storage.getClient(assignment.clientId);\n\n          return {\n            ...assignment,\n            doctorName: doctor?.name,\n            clientName: client?.name,\n          };\n        })\n      );\n\n      res.json(enrichedAssignments);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n\n  // Admin - Create doctor-client assignment\n  app.post('/api/admin/assignments', isAdmin, async (req, res) => {\n    try {\n      const { doctorId, clientId } = req.body;\n\n      // Validate doctor and client existence\n      const doctor = await storage.getUser(doctorId);\n      const client = await storage.getClient(clientId);\n\n      if (!doctor || doctor.userRole !== 'doctor') {\n        return res.status(400).json({ message: 'Invalid doctor ID' });\n      }\n\n      if (!client) {\n        return res.status(400).json({ message: 'Invalid client ID' });\n      }\n\n      const assignment = await storage.createDoctorClient({ doctorId, clientId });\n      res.status(201).json(assignment);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n\n  // Admin - Get invitation codes\n  app.get('/api/admin/invitation-codes', isAdmin, async (req, res) => {\n    try {\n      const codes = await storage.getAllInvitationCodes();\n\n      // Enrich with user names\n      const enrichedCodes = await Promise.all(\n        codes.map(async (code) => {\n          const creator = await storage.getUser(code.createdBy);\n          const usedBy = code.usedBy ? await storage.getUser(code.usedBy) : null;\n\n          return {\n            ...code,\n            creatorName: creator?.name,\n            usedByName: usedBy?.name,\n          };\n        })\n      );\n\n      res.json(enrichedCodes);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n\n  // Admin - Create invitation code\n  app.post('/api/admin/invitation-codes', isAdmin, async (req, res) => {\n    try {\n      const { type } = req.body;\n\n      // Generate a unique code\n      const generateCode = () => {\n        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n        let result = '';\n        for (let i = 0; i < 8; i++) {\n          result += chars.charAt(Math.floor(Math.random() * chars.length));\n        }\n        return result;\n      };\n\n      let code = generateCode();\n      let attempts = 0;\n\n      // Ensure code is unique\n      while (attempts < 10) {\n        const existing = await storage.getInvitationCodeByCode(code);\n        if (!existing) break;\n        code = generateCode();\n        attempts++;\n      }\n\n      if (attempts >= 10) {\n        return res.status(500).json({ message: 'Failed to generate unique invitation code' });\n      }\n\n      const codeData = {\n        code,\n        type,\n        createdBy: req.user!.id,\n      };\n\n      const invitationCode = await storage.createInvitationCode(codeData);\n      res.status(201).json(invitationCode);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n\n  // ------------------------\n  // ANALYTICS ROUTES\n  // ------------------------\n\n  // Get analytics data\n  app.get('/api/analytics', isAuthenticated, async (req, res) => {\n    try {\n      const userId = req.user!.id;\n      const userRole = req.user!.userRole;\n\n      // Get date range from query params (default to last 30 days)\n      const endDate = new Date();\n      const startDate = new Date();\n      startDate.setDate(startDate.getDate() - 30);\n\n      let analytics;\n\n      if (userRole === 'admin') {\n        // Admin sees system-wide analytics\n        analytics = await getSystemAnalytics(startDate, endDate);\n      } else if (userRole === 'doctor') {\n        // Doctor sees their client analytics\n        analytics = await getDoctorAnalytics(userId, startDate, endDate);\n      } else {\n        // Client sees their own analytics\n        analytics = await getClientAnalytics(userId, startDate, endDate);\n      }\n\n      res.json(analytics);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n\n  // Get weekly activity data\n  app.get('/api/analytics/weekly', isAuthenticated, async (req, res) => {\n    try {\n      const userId = req.user!.id;\n      const userRole = req.user!.userRole;\n\n      const weeklyData = await getWeeklyActivity(userId, userRole);\n      res.json(weeklyData);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n\n  // Get monthly trends\n  app.get('/api/analytics/monthly', isAuthenticated, async (req, res) => {\n    try {\n      const userId = req.user!.id;\n      const userRole = req.user!.userRole;\n\n      const monthlyData = await getMonthlyTrends(userId, userRole);\n      res.json(monthlyData);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Update user\n  app.patch('/api/users/:id', isAuthenticated, async (req, res) => {\n    try {\n      const userId = parseInt(req.params.id);\n      \n      // Only allow users to update themselves or admins to update anyone\n      if (req.user?.id !== userId && req.user?.userRole !== 'admin') {\n        return res.status(403).json({ message: 'Not authorized to update this user' });\n      }\n      \n      const userData = req.body;\n      \n      // Prevent role escalation by non-admins\n      if (userData.userRole && req.user?.userRole !== 'admin') {\n        delete userData.userRole;\n      }\n      \n      const user = await storage.updateUser(Number(userId), userData);\n      \n      if (!user) {\n        return res.status(404).json({ message: 'User not found' });\n      }\n      \n      res.json(user);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // ------------------------\n  // DASHBOARD ROUTES\n  // ------------------------\n  \n  // Get dashboard stats\n  app.get('/api/dashboard/stats', isAuthenticated, async (req, res) => {\n    try {\n      const userId = req.user?.id;\n      \n      if (!userId || isNaN(userId)) {\n        return res.status(401).json({ message: 'User ID is required' });\n      }\n      \n      // Use custom SQL query instead of ORM method\n      const stats = await getDashboardStatsSQL(pool, Number(userId));\n      res.json(stats);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // ------------------------\n  // INVITE CODE ROUTES\n  // ------------------------\n  \n  // Create a new invitation code\n  app.post('/api/invites', isAuthenticated, async (req, res) => {\n    try {\n      // Validate user permissions to create invitation codes\n      if (req.user?.userRole === 'client') {\n        return res.status(403).json({ message: 'Clients cannot create invitation codes' });\n      }\n      \n      if (req.user?.userRole === 'doctor' && req.body.type !== 'client') {\n        return res.status(403).json({ message: 'Doctors can only create client invitation codes' });\n      }\n      \n      // Generate a random code if not provided\n      const code = req.body.code || randomInvitationCode();\n      \n      const inviteData = insertInvitationCodeSchema.parse({\n        ...req.body,\n        code,\n        createdBy: req.user?.id\n      });\n      \n      const invite = await storage.createInvitationCode(inviteData);\n      res.status(201).json(invite);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Get invitation codes created by the user\n  app.get('/api/invites', isAuthenticated, async (req, res) => {\n    try {\n      const userId = req.user?.id;\n      if (!userId || isNaN(userId)) {\n        return res.status(401).json({ message: 'User ID is required' });\n      }\n      \n      const type = req.query.type as 'doctor' | 'client' | undefined;\n      \n      const codes = await storage.getInvitationCodesByCreator(Number(userId), type);\n      res.json(codes);\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Check if an invitation code is valid\n  app.get('/api/invites/:code', async (req, res) => {\n    try {\n      const code = req.params.code;\n      const inviteCode = await storage.getInvitationCodeByCode(code);\n      \n      if (!inviteCode) {\n        return res.status(404).json({ message: 'Invitation code not found' });\n      }\n      \n      if (inviteCode.used) {\n        return res.status(400).json({ message: 'Invitation code has already been used' });\n      }\n      \n      if (inviteCode.expiresAt && new Date(inviteCode.expiresAt) < new Date()) {\n        return res.status(400).json({ message: 'Invitation code has expired' });\n      }\n      \n      res.json({\n        valid: true,\n        type: inviteCode.type\n      });\n    } catch (error) {\n      handleError(res, error);\n    }\n  });\n  \n  // Create HTTP server\n  const httpServer = createServer(app);\n  \n  // Test voice endpoint\n  app.get('/api/test-voice', async (req, res) => {\n    try {\n      // Extract parameters from query\n      const voice = req.query.voice?.toString() || 'shimmer';\n      const speed = parseFloat(req.query.speed?.toString() || '1.0');\n      \n      // Test phrases for each voice\n      const testPhrases: Record<string, string> = {\n        'alloy': 'This is a test of the Alloy voice. Can you hear this clearly?',\n        'echo': 'Testing the Echo voice. Please confirm if the audio is working properly.',\n        'fable': 'The Fable voice is being tested now. Is the sound quality good?',\n        'onyx': 'This is the Onyx voice test. Can you hear this message?',\n        'nova': 'Nova voice test in progress. Please verify the audio output.',\n        'shimmer': 'Testing the Shimmer voice output. Is this coming through clearly?'\n      };\n      \n      const text = testPhrases[voice] || 'This is a test of the voice output system. Can you hear this clearly?';\n      \n      console.log(`Processing test audio request: ${text}`);\n      \n      // Ensure uploads directory exists\n      const uploadsDir = path.join(process.cwd(), 'uploads', 'audio');\n      if (!fs.existsSync(uploadsDir)) {\n        fs.mkdirSync(uploadsDir, { recursive: true });\n      }\n      \n      // Generate unique filename\n      const filename = `speech-${uuidv4()}.mp3`;\n      const filePath = path.join(uploadsDir, filename);\n      const fileUrl = `/uploads/audio/${filename}`;\n      \n      // Create an instance of the OpenAI client\n      const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });\n      \n      // Generate speech using OpenAI API\n      const mp3Response = await openai.audio.speech.create({\n        model: \"tts-1\",\n        voice: voice as any,\n        input: text,\n        speed: speed\n      });\n      \n      // Convert to buffer and save to file\n      const buffer = Buffer.from(await mp3Response.arrayBuffer());\n      fs.writeFileSync(filePath, buffer);\n      \n      console.log(`Sent test audio response with URL: ${fileUrl}`);\n      \n      // Return URL to audio file\n      res.json({ url: fileUrl });\n      \n    } catch (error) {\n      console.error('Error generating test voice:', error);\n      res.status(500).json({ \n        error: 'Failed to generate test voice', \n        details: error instanceof Error ? error.message : String(error)\n      });\n    }\n  });\n\n  // WebSocket server is set up in index.ts\n\n  return httpServer;\n}\n\n// ------------------------\n// ANALYTICS HELPER FUNCTIONS\n// ------------------------\n\nasync function getSystemAnalytics(startDate: Date, endDate: Date) {\n  // Get total conversations, users, and other system metrics\n  const conversations = await storage.getAllConversations();\n  const users = await storage.getAllUsers();\n  const clients = await storage.getAllClients();\n\n  // Filter conversations by date range\n  const recentConversations = conversations.filter(conv => {\n    const convDate = new Date(conv.createdAt);\n    return convDate >= startDate && convDate <= endDate;\n  });\n\n  // Calculate metrics\n  const totalSessions = recentConversations.length;\n  const totalUsers = users.length;\n  const totalClients = clients.length;\n  const activeDoctors = users.filter(u => u.userRole === 'doctor').length;\n\n  // Calculate average session duration (if available)\n  const avgSessionDuration = recentConversations.length > 0\n    ? recentConversations.reduce((sum, conv) => {\n        if (conv.endedAt && conv.createdAt) {\n          const duration = new Date(conv.endedAt).getTime() - new Date(conv.createdAt).getTime();\n          return sum + duration;\n        }\n        return sum;\n      }, 0) / recentConversations.length / 1000 / 60 // Convert to minutes\n    : 0;\n\n  return {\n    totalSessions,\n    totalUsers,\n    totalClients,\n    activeDoctors,\n    avgSessionDuration: Math.round(avgSessionDuration),\n    sessionGrowth: calculateGrowthRate(conversations, startDate, endDate),\n    userGrowth: calculateUserGrowthRate(users, startDate, endDate)\n  };\n}\n\nasync function getDoctorAnalytics(doctorId: number, startDate: Date, endDate: Date) {\n  // Get doctor's clients and their conversations\n  const doctorClients = await storage.getClientsByDoctor(doctorId);\n  const clientIds = doctorClients.map(c => c.id);\n\n  // Get conversations for doctor's clients\n  const allConversations = await storage.getAllConversations();\n  const doctorConversations = allConversations.filter(conv =>\n    clientIds.includes(conv.clientId) &&\n    new Date(conv.createdAt) >= startDate &&\n    new Date(conv.createdAt) <= endDate\n  );\n\n  const totalSessions = doctorConversations.length;\n  const activeClients = new Set(doctorConversations.map(c => c.clientId)).size;\n  const totalClients = doctorClients.length;\n\n  // Calculate average session duration\n  const avgSessionDuration = doctorConversations.length > 0\n    ? doctorConversations.reduce((sum, conv) => {\n        if (conv.endedAt && conv.createdAt) {\n          const duration = new Date(conv.endedAt).getTime() - new Date(conv.createdAt).getTime();\n          return sum + duration;\n        }\n        return sum;\n      }, 0) / doctorConversations.length / 1000 / 60\n    : 0;\n\n  return {\n    totalSessions,\n    activeClients,\n    totalClients,\n    avgSessionDuration: Math.round(avgSessionDuration),\n    sessionGrowth: calculateGrowthRate(doctorConversations, startDate, endDate)\n  };\n}\n\nasync function getClientAnalytics(clientId: number, startDate: Date, endDate: Date) {\n  // Get client's conversations\n  const allConversations = await storage.getAllConversations();\n  const clientConversations = allConversations.filter(conv =>\n    conv.clientId === clientId &&\n    new Date(conv.createdAt) >= startDate &&\n    new Date(conv.createdAt) <= endDate\n  );\n\n  const totalSessions = clientConversations.length;\n\n  // Calculate total session time\n  const totalSessionTime = clientConversations.reduce((sum, conv) => {\n    if (conv.endedAt && conv.createdAt) {\n      const duration = new Date(conv.endedAt).getTime() - new Date(conv.createdAt).getTime();\n      return sum + duration;\n    }\n    return sum;\n  }, 0) / 1000 / 60; // Convert to minutes\n\n  const avgSessionDuration = totalSessions > 0 ? totalSessionTime / totalSessions : 0;\n\n  // Get recent session dates for streak calculation\n  const sessionDates = clientConversations\n    .map(conv => new Date(conv.createdAt).toDateString())\n    .filter((date, index, arr) => arr.indexOf(date) === index)\n    .sort();\n\n  return {\n    totalSessions,\n    totalSessionTime: Math.round(totalSessionTime),\n    avgSessionDuration: Math.round(avgSessionDuration),\n    sessionStreak: calculateSessionStreak(sessionDates),\n    recentActivity: sessionDates.slice(-7) // Last 7 unique session dates\n  };\n}\n\nasync function getWeeklyActivity(userId: number, userRole: string) {\n  const endDate = new Date();\n  const startDate = new Date();\n  startDate.setDate(startDate.getDate() - 49); // 7 weeks\n\n  const allConversations = await storage.getAllConversations();\n  let relevantConversations;\n\n  if (userRole === 'admin') {\n    relevantConversations = allConversations;\n  } else if (userRole === 'doctor') {\n    const doctorClients = await storage.getClientsByDoctor(userId);\n    const clientIds = doctorClients.map(c => c.id);\n    relevantConversations = allConversations.filter(conv => clientIds.includes(conv.clientId));\n  } else {\n    relevantConversations = allConversations.filter(conv => conv.clientId === userId);\n  }\n\n  // Group by week\n  const weeklyData = [];\n  for (let i = 6; i >= 0; i--) {\n    const weekStart = new Date();\n    weekStart.setDate(weekStart.getDate() - (i * 7));\n    weekStart.setHours(0, 0, 0, 0);\n\n    const weekEnd = new Date(weekStart);\n    weekEnd.setDate(weekEnd.getDate() + 6);\n    weekEnd.setHours(23, 59, 59, 999);\n\n    const weekConversations = relevantConversations.filter(conv => {\n      const convDate = new Date(conv.createdAt);\n      return convDate >= weekStart && convDate <= weekEnd;\n    });\n\n    weeklyData.push({\n      week: `Week ${7 - i}`,\n      sessions: weekConversations.length,\n      date: weekStart.toISOString().split('T')[0]\n    });\n  }\n\n  return weeklyData;\n}\n\nasync function getMonthlyTrends(userId: number, userRole: string) {\n  const endDate = new Date();\n  const startDate = new Date();\n  startDate.setMonth(startDate.getMonth() - 11); // 12 months\n\n  const allConversations = await storage.getAllConversations();\n  let relevantConversations;\n\n  if (userRole === 'admin') {\n    relevantConversations = allConversations;\n  } else if (userRole === 'doctor') {\n    const doctorClients = await storage.getClientsByDoctor(userId);\n    const clientIds = doctorClients.map(c => c.id);\n    relevantConversations = allConversations.filter(conv => clientIds.includes(conv.clientId));\n  } else {\n    relevantConversations = allConversations.filter(conv => conv.clientId === userId);\n  }\n\n  // Group by month\n  const monthlyData = [];\n  for (let i = 11; i >= 0; i--) {\n    const monthStart = new Date();\n    monthStart.setMonth(monthStart.getMonth() - i);\n    monthStart.setDate(1);\n    monthStart.setHours(0, 0, 0, 0);\n\n    const monthEnd = new Date(monthStart);\n    monthEnd.setMonth(monthEnd.getMonth() + 1);\n    monthEnd.setDate(0);\n    monthEnd.setHours(23, 59, 59, 999);\n\n    const monthConversations = relevantConversations.filter(conv => {\n      const convDate = new Date(conv.createdAt);\n      return convDate >= monthStart && convDate <= monthEnd;\n    });\n\n    monthlyData.push({\n      month: monthStart.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),\n      sessions: monthConversations.length,\n      date: monthStart.toISOString().split('T')[0]\n    });\n  }\n\n  return monthlyData;\n}\n\nfunction calculateGrowthRate(conversations: any[], startDate: Date, endDate: Date) {\n  const midDate = new Date((startDate.getTime() + endDate.getTime()) / 2);\n\n  const firstHalf = conversations.filter(conv => {\n    const convDate = new Date(conv.createdAt);\n    return convDate >= startDate && convDate < midDate;\n  }).length;\n\n  const secondHalf = conversations.filter(conv => {\n    const convDate = new Date(conv.createdAt);\n    return convDate >= midDate && convDate <= endDate;\n  }).length;\n\n  if (firstHalf === 0) return secondHalf > 0 ? 100 : 0;\n  return Math.round(((secondHalf - firstHalf) / firstHalf) * 100);\n}\n\nfunction calculateUserGrowthRate(users: any[], startDate: Date, endDate: Date) {\n  const midDate = new Date((startDate.getTime() + endDate.getTime()) / 2);\n\n  const firstHalf = users.filter(user => {\n    const userDate = new Date(user.createdAt);\n    return userDate >= startDate && userDate < midDate;\n  }).length;\n\n  const secondHalf = users.filter(user => {\n    const userDate = new Date(user.createdAt);\n    return userDate >= midDate && userDate <= endDate;\n  }).length;\n\n  if (firstHalf === 0) return secondHalf > 0 ? 100 : 0;\n  return Math.round(((secondHalf - firstHalf) / firstHalf) * 100);\n}\n\nfunction calculateSessionStreak(sessionDates: string[]) {\n  if (sessionDates.length === 0) return 0;\n\n  let streak = 1;\n  let maxStreak = 1;\n\n  for (let i = 1; i < sessionDates.length; i++) {\n    const prevDate = new Date(sessionDates[i - 1]);\n    const currDate = new Date(sessionDates[i]);\n    const dayDiff = (currDate.getTime() - prevDate.getTime()) / (1000 * 60 * 60 * 24);\n\n    if (dayDiff <= 7) { // Within a week\n      streak++;\n      maxStreak = Math.max(maxStreak, streak);\n    } else {\n      streak = 1;\n    }\n  }\n\n  return maxStreak;\n}\n"}