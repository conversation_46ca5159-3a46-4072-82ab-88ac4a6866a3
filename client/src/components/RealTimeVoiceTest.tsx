import React, { useState, useEffect, useRef } from 'react';
import { VoiceConfig, ConversationConfig } from '@shared/schema';

interface Metrics {
  latency: number;
  audioQuality: number;
  conversationFlow: number;
  responseTime: number;
}

interface RealTimeVoiceTestProps {
  onConfigChange: (config: { voice: VoiceConfig; conversation: ConversationConfig }) => void;
}

export const RealTimeVoiceTest: React.FC<RealTimeVoiceTestProps> = ({ onConfigChange }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [metrics, setMetrics] = useState<Metrics>({
    latency: 0,
    audioQuality: 0,
    conversationFlow: 0,
    responseTime: 0
  });
  const [voiceConfig, setVoiceConfig] = useState<VoiceConfig>({
    voice: "shimmer",
    speed: 1.05,
    pitch: 1.0,
    emphasis: 'moderate',
    prosody: {
      emotionalRange: 0.6,
      questionInflection: 0.7,
      pauseDuration: 300
    }
  });
  const [conversationConfig, setConversationConfig] = useState<ConversationConfig>({
    temperature: 0.8,
    maxTokens: 1000,
    presencePenalty: 0.1,
    frequencyPenalty: 0.1,
    turnTaking: {
      backchannelFrequency: 0.3,
      minSilenceDuration: 300,
      maxInterruptionGap: 500
    },
    responseStyle: {
      minResponseLength: 50,
      maxResponseLength: 150,
      temperature: 0.8,
      presencePenalty: 0.1,
      frequencyPenalty: 0.1
    }
  });

  const wsRef = useRef<WebSocket | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  useEffect(() => {
    // Initialize WebSocket connection
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws`;
    const ws = new WebSocket(wsUrl);
    wsRef.current = ws;

    ws.onopen = () => {
      setIsConnected(true);
      console.log('WebSocket connected');
    };

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      handleWebSocketMessage(data);
    };

    ws.onclose = () => {
      setIsConnected(false);
      console.log('WebSocket disconnected');
    };

    return () => {
      ws.close();
    };
  }, []);

  const handleWebSocketMessage = (data: any) => {
    switch (data.type) {
      case 'metrics_update':
        setMetrics(prev => ({
          ...prev,
          latency: data.metrics.latency,
          responseTime: data.metrics.responseTime
        }));
        break;
      case 'audio_chunk':
        playAudioChunk(data.audioData);
        break;
      case 'error':
        console.error('WebSocket error:', data.message);
        break;
    }
  };

  const playAudioChunk = (base64Audio: string) => {
    if (!audioContextRef.current) {
      audioContextRef.current = new AudioContext();
    }

    const audioData = Uint8Array.from(atob(base64Audio), c => c.charCodeAt(0));
    audioContextRef.current.decodeAudioData(audioData.buffer, (buffer) => {
      const source = audioContextRef.current!.createBufferSource();
      source.buffer = buffer;
      source.connect(audioContextRef.current!.destination);
      source.start();
    });
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
          // Send audio chunk to server
          wsRef.current?.send(JSON.stringify({
            type: 'audio',
            audioData: event.data
          }));
        }
      };

      mediaRecorder.start(100); // Capture audio in 100ms chunks
      setIsRecording(true);
    } catch (error) {
      console.error('Error starting recording:', error);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const handleVoiceConfigChange = (key: keyof VoiceConfig, value: any) => {
    const newConfig = { ...voiceConfig, [key]: value };
    setVoiceConfig(newConfig);
    onConfigChange({ voice: newConfig, conversation: conversationConfig });
  };

  const handleConversationConfigChange = (key: keyof ConversationConfig, value: any) => {
    const newConfig = { ...conversationConfig, [key]: value };
    setConversationConfig(newConfig);
    onConfigChange({ voice: voiceConfig, conversation: newConfig });
  };

  return (
    <div className="p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold">Real-Time Voice Test</h2>
        <div className="flex items-center space-x-2">
          <span className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Voice Configuration */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Voice Configuration</h3>
          <div className="space-y-2">
            <label className="block">
              Voice:
              <select
                value={voiceConfig.voice}
                onChange={(e) => handleVoiceConfigChange('voice', e.target.value)}
                className="w-full p-2 border rounded"
              >
                <option value="shimmer">Shimmer</option>
                <option value="alloy">Alloy</option>
                <option value="echo">Echo</option>
                <option value="fable">Fable</option>
                <option value="onyx">Onyx</option>
                <option value="nova">Nova</option>
              </select>
            </label>

            <label className="block">
              Speed:
              <input
                type="range"
                min="0.5"
                max="2"
                step="0.05"
                value={voiceConfig.speed}
                onChange={(e) => handleVoiceConfigChange('speed', parseFloat(e.target.value))}
                className="w-full"
              />
              <span>{voiceConfig.speed.toFixed(2)}x</span>
            </label>

            <label className="block">
              Emphasis:
              <select
                value={voiceConfig.emphasis}
                onChange={(e) => handleVoiceConfigChange('emphasis', e.target.value)}
                className="w-full p-2 border rounded"
              >
                <option value="strong">Strong</option>
                <option value="moderate">Moderate</option>
                <option value="subtle">Subtle</option>
              </select>
            </label>
          </div>
        </div>

        {/* Conversation Configuration */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Conversation Configuration</h3>
          <div className="space-y-2">
            <label className="block">
              Backchannel Frequency:
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={conversationConfig.turnTaking.backchannelFrequency}
                onChange={(e) => handleConversationConfigChange('turnTaking', {
                  ...conversationConfig.turnTaking,
                  backchannelFrequency: parseFloat(e.target.value)
                })}
                className="w-full"
              />
              <span>{(conversationConfig.turnTaking.backchannelFrequency * 100).toFixed(0)}%</span>
            </label>

            <label className="block">
              Min Silence Duration:
              <input
                type="range"
                min="100"
                max="1000"
                step="50"
                value={conversationConfig.turnTaking.minSilenceDuration}
                onChange={(e) => handleConversationConfigChange('turnTaking', {
                  ...conversationConfig.turnTaking,
                  minSilenceDuration: parseInt(e.target.value)
                })}
                className="w-full"
              />
              <span>{conversationConfig.turnTaking.minSilenceDuration}ms</span>
            </label>

            <label className="block">
              Response Temperature:
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={conversationConfig.responseStyle.temperature}
                onChange={(e) => handleConversationConfigChange('responseStyle', {
                  ...conversationConfig.responseStyle,
                  temperature: parseFloat(e.target.value)
                })}
                className="w-full"
              />
              <span>{conversationConfig.responseStyle.temperature.toFixed(1)}</span>
            </label>
          </div>
        </div>
      </div>

      {/* Metrics Display */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="p-4 bg-gray-100 rounded">
          <h4 className="font-semibold">Latency</h4>
          <p className="text-2xl">{metrics.latency}ms</p>
        </div>
        <div className="p-4 bg-gray-100 rounded">
          <h4 className="font-semibold">Audio Quality</h4>
          <p className="text-2xl">{metrics.audioQuality}/10</p>
        </div>
        <div className="p-4 bg-gray-100 rounded">
          <h4 className="font-semibold">Conversation Flow</h4>
          <p className="text-2xl">{metrics.conversationFlow}/10</p>
        </div>
        <div className="p-4 bg-gray-100 rounded">
          <h4 className="font-semibold">Response Time</h4>
          <p className="text-2xl">{metrics.responseTime}ms</p>
        </div>
      </div>

      {/* Controls */}
      <div className="flex justify-center space-x-4">
        <button
          onClick={isRecording ? stopRecording : startRecording}
          className={`px-4 py-2 rounded ${
            isRecording ? 'bg-red-500 hover:bg-red-600' : 'bg-blue-500 hover:bg-blue-600'
          } text-white`}
        >
          {isRecording ? 'Stop Recording' : 'Start Recording'}
        </button>
      </div>
    </div>
  );
}; 